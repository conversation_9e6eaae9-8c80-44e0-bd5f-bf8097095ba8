# Permission System Migration Guide

## 🚀 Quick Migration Script

Use this guide to quickly update your existing controllers to use the simplified permission system.

**Important**: The system only supports two scopes: `PLATFORM` (for super admins) and `COMPANY` (for company users). There is no "both" option.

## Step 1: Update Imports

### Before

```typescript
import {
  RequireViewPermission,
  RequireCreatePermission,
  RequireEditPermission,
  RequireDeletePermission,
  RequirePlatformViewPermission,
  RequirePlatformCreatePermission,
  RequirePlatformEditPermission,
  RequirePlatformDeletePermission,
} from '../auth/decorators/permission.decorator';
```

### After

```typescript
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
  Permission,
} from '../auth/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM, ROLE_SCOPE_ENUM } from '../../../utils/enums';
```

## Step 2: Update Decorators

### Search & Replace Patterns

Use these find/replace patterns in your IDE:

#### Basic Permissions

- `@RequireViewPermission(` → `@CanView(`
- `@RequireCreatePermission(` → `@CanCreate(`
- `@RequireEditPermission(` → `@CanEdit(`
- `@RequireDeletePermission(` → `@CanDelete(`

Then add `, ROLE_SCOPE_ENUM.COMPANY` as the second parameter for company permissions.

#### Platform Permissions

- `@RequirePlatformViewPermission(` → `@CanView(`
- `@RequirePlatformCreatePermission(` → `@CanCreate(`
- `@RequirePlatformEditPermission(` → `@CanEdit(`
- `@RequirePlatformDeletePermission(` → `@CanDelete(`

Then add `, ROLE_SCOPE_ENUM.PLATFORM` as the second parameter for platform permissions.

## Step 3: Update Guard Usage

### Before

```typescript
@Controller('users')
@UseGuards(AuthPermissionGuard)
```

### After

```typescript
@Controller('users')
@UseGuards(PermissionGuard)
```

## Step 4: Example Migration

### Before

```typescript
import { Controller, Get, Post, Put, Delete, UseGuards } from '@nestjs/common';
import { AuthPermissionGuard } from '../auth/guards/auth-permission.guard';
import {
  RequireViewPermission,
  RequireCreatePermission,
  RequireEditPermission,
  RequireDeletePermission,
  RequirePlatformViewPermission,
} from '../auth/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from '../../../utils/enums';

@Controller('users')
@UseGuards(AuthPermissionGuard)
export class UsersController {
  @Get()
  @RequireViewPermission(PLATFORM_SECTION_ENUM.USERS)
  findAll() {
    return this.usersService.findAll();
  }

  @Post()
  @RequireCreatePermission(PLATFORM_SECTION_ENUM.USERS)
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Put(':id')
  @RequireEditPermission(PLATFORM_SECTION_ENUM.USERS)
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(id, updateUserDto);
  }

  @Delete(':id')
  @RequireDeletePermission(PLATFORM_SECTION_ENUM.USERS)
  remove(@Param('id') id: string) {
    return this.usersService.remove(id);
  }

  @Get('admin/all')
  @RequirePlatformViewPermission(PLATFORM_SECTION_ENUM.USERS)
  findAllFromAllCompanies() {
    return this.usersService.findAllFromAllCompanies();
  }
}
```

### After

```typescript
import { Controller, Get, Post, Put, Delete, UseGuards } from '@nestjs/common';
import { PermissionGuard } from '../auth/guards/permission.guard';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
} from '../auth/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM, ROLE_SCOPE_ENUM } from '../../../utils/enums';

@Controller('users')
@UseGuards(PermissionGuard)
export class UsersController {
  @Get()
  @CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)
  findCompanyUsers() {
    return this.usersService.findCompanyUsers();
  }

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(id, updateUserDto);
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)
  remove(@Param('id') id: string) {
    return this.usersService.remove(id);
  }

  @Get('admin/all')
  @CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.PLATFORM)
  findAllFromAllCompanies() {
    return this.usersService.findAllFromAllCompanies();
  }
}
```

## Step 5: Advanced Migrations

### Complex Permission Requirements

#### Before

```typescript
@RequirePermission({
  section: PLATFORM_SECTION_ENUM.EMPLOYEES,
  action: 'canView',
  requireCompanyScope: true,
  allowPlatformScope: false
})
```

#### After

```typescript
@CanView(PLATFORM_SECTION_ENUM.EMPLOYEES, ROLE_SCOPE_ENUM.COMPANY)
```

### Custom Permission Logic

#### Before

```typescript
@RequirePermission({
  section: PLATFORM_SECTION_ENUM.PAYROLLS,
  action: 'canCreate',
  requireCompanyScope: true,
  allowPlatformScope: true
})
```

#### After

```typescript
@Permission(
  PLATFORM_SECTION_ENUM.PAYROLLS,
  PERMISSION_ACTION_ENUM.CREATE,
  { scope: ROLE_SCOPE_ENUM.COMPANY }
)
```

## Step 6: Remove Duplicate Admin Controllers

### Before (Separate Controllers)

```typescript
// admin.users.controller.ts
@Controller('admin/users')
export class AdminUsersController {
  @Get()
  @RequirePlatformViewPermission(PLATFORM_SECTION_ENUM.USERS)
  findAll() {
    return this.usersService.findAllFromAllCompanies();
  }
}

// users.controller.ts
@Controller('users')
export class UsersController {
  @Get()
  @RequireViewPermission(PLATFORM_SECTION_ENUM.USERS)
  findAll() {
    return this.usersService.findAll();
  }
}
```

### After (Unified Controller)

```typescript
// users.controller.ts
@Controller('users')
@UseGuards(PermissionGuard)
export class UsersController {
  @Get()
  @CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)
  findCompanyUsers() {
    return this.usersService.findCompanyUsers();
  }

  @Get('admin/all')
  @CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.PLATFORM)
  findAllUsers() {
    return this.usersService.findAllFromAllCompanies();
  }
}
```

## Step 7: Test Your Migration

### Test Commands

```bash
# Test company user permissions
curl -X GET "{{base_url}}/users" \
  -H "Authorization: Bearer <company-user-token>"

# Test platform admin permissions
curl -X GET "{{base_url}}/users/admin/all" \
  -H "Authorization: Bearer <platform-admin-token>"

# Test permission check
curl -X POST "{{base_url}}/permissions/check" \
  -H "Authorization: Bearer <jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{"section": "users", "action": "canView", "companyId": 1}'
```

## Step 8: Cleanup

### Remove Old Imports

After migration, remove these unused imports:

- `AuthPermissionGuard`
- `RequireViewPermission`
- `RequireCreatePermission`
- `RequireEditPermission`
- `RequireDeletePermission`
- `RequirePlatformViewPermission`
- `RequirePlatformCreatePermission`
- `RequirePlatformEditPermission`
- `RequirePlatformDeletePermission`

### Remove Duplicate Controllers

Delete the separate admin controllers:

- `admin.users.controller.ts`
- `admin.roles.controller.ts`
- `admin.permissions.controller.ts`
- etc.

## Migration Checklist

- [ ] Updated imports to use new decorators
- [ ] Replaced old decorators with new ones
- [ ] Updated guard usage (`AuthPermissionGuard` → `PermissionGuard`)
- [ ] Added explicit scope parameters (ROLE_SCOPE_ENUM.COMPANY or ROLE_SCOPE_ENUM.PLATFORM)
- [ ] Unified admin and regular controllers
- [ ] Tested all endpoints with different user roles
- [ ] Removed unused imports and files
- [ ] Updated documentation/comments

## Benefits After Migration

✅ **75% fewer decorators** to maintain  
✅ **Cleaner, more readable code**  
✅ **Better TypeScript support**  
✅ **Unified controller structure**  
✅ **Explicit permission scoping**  
✅ **Improved performance**  
✅ **Easier testing and debugging**  
✅ **Clear scope separation** (PLATFORM vs COMPANY)

## Need Help?

Check the examples in:

- `src/modules/auth/controllers/example.controller.ts`
- `PERMISSION_USAGE_GUIDE.md`
- `PERMISSION_SYSTEM_README.md`

The simplified system maintains backward compatibility, so you can migrate gradually!
