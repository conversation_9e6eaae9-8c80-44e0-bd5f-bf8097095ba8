# Simplified Permission System Usage Guide

## 🎯 Overview

The permission system has been **simplified** to reduce complexity while maintaining all functionality. The new system uses just **2 main decorators** instead of 8, with cleaner syntax and better TypeScript support.

**Important**: The system only supports two scopes: `PLATFORM` (for super admins) and `COMPANY` (for company users). There is no "both" option.

## 🚀 Quick Start

### 1. Basic Setup

```typescript
import { Controller, Get, Post, UseGuards } from '@nestjs/common';
import { PermissionGuard } from '../auth/guards/permission.guard';
import { CanView, CanCreate } from '../auth/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM, ROLE_SCOPE_ENUM } from '../../../utils/enums';

@Controller('users')
@UseGuards(PermissionGuard)
export class UsersController {
  @Get()
  @CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)
  findAll() {
    return this.usersService.findAll();
  }

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }
}
```

## 📋 New Simplified Decorators

### Main Decorators (Recommended)

#### 1. `@Permission()` - The Universal Decorator

```typescript
@Permission(section, action?, options?)
```

**Examples:**

```typescript
// Basic view permission (company scope)
@Permission(PLATFORM_SECTION_ENUM.USERS)

// Specific action with platform scope
@Permission(PLATFORM_SECTION_ENUM.USERS, PERMISSION_ACTION_ENUM.CREATE, { scope: ROLE_SCOPE_ENUM.PLATFORM })

// With company scope control
@Permission(PLATFORM_SECTION_ENUM.USERS, PERMISSION_ACTION_ENUM.EDIT, { scope: ROLE_SCOPE_ENUM.COMPANY })
```

#### 2. CRUD Convenience Decorators

```typescript
@CanView(section, scope)     // View permission
@CanCreate(section, scope)   // Create permission
@CanEdit(section, scope)     // Edit permission
@CanDelete(section, scope)   // Delete permission
```

**Examples:**

```typescript
@CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)        // Company scope only
@CanCreate(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)      // Company scope only
@CanEdit(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.PLATFORM)       // Platform scope only
@CanDelete(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)      // Company scope only
```

### Scope Options

| Scope                      | Description         | Who Can Access       |
| -------------------------- | ------------------- | -------------------- |
| `ROLE_SCOPE_ENUM.COMPANY`  | Company scope only  | Company users only   |
| `ROLE_SCOPE_ENUM.PLATFORM` | Platform scope only | Platform admins only |

## 🔄 Migration Guide

### Before (8 decorators)

```typescript
// Old system - too many decorators
@RequireViewPermission(PLATFORM_SECTION_ENUM.USERS)
@RequireCreatePermission(PLATFORM_SECTION_ENUM.USERS)
@RequireEditPermission(PLATFORM_SECTION_ENUM.USERS)
@RequireDeletePermission(PLATFORM_SECTION_ENUM.USERS)
@RequirePlatformViewPermission(PLATFORM_SECTION_ENUM.USERS)
@RequirePlatformCreatePermission(PLATFORM_SECTION_ENUM.USERS)
@RequirePlatformEditPermission(PLATFORM_SECTION_ENUM.USERS)
@RequirePlatformDeletePermission(PLATFORM_SECTION_ENUM.USERS)
```

### After (2 main decorators)

```typescript
// New system - clean and simple
@CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)        // Company scope
@CanCreate(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)      // Company scope
@CanEdit(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)        // Company scope
@CanDelete(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)      // Company scope
@CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.PLATFORM)       // Platform scope
@CanCreate(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.PLATFORM)     // Platform scope
@CanEdit(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.PLATFORM)       // Platform scope
@CanDelete(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.PLATFORM)     // Platform scope
```

### Migration Examples

#### 1. Basic CRUD Operations

```typescript
// OLD
@RequireViewPermission(PLATFORM_SECTION_ENUM.EMPLOYEES)
@RequireCreatePermission(PLATFORM_SECTION_ENUM.EMPLOYEES)

// NEW
@CanView(PLATFORM_SECTION_ENUM.EMPLOYEES, ROLE_SCOPE_ENUM.COMPANY)
@CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES, ROLE_SCOPE_ENUM.COMPANY)
```

#### 2. Platform-Only Operations

```typescript
// OLD
@RequirePlatformViewPermission(PLATFORM_SECTION_ENUM.USERS)

// NEW
@CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.PLATFORM)
```

#### 3. Company-Only Operations

```typescript
// OLD
@RequirePermission({
  section: PLATFORM_SECTION_ENUM.EMPLOYEES,
  action: 'canView',
  requireCompanyScope: true,
  allowPlatformScope: false
})

// NEW
@CanView(PLATFORM_SECTION_ENUM.EMPLOYEES, ROLE_SCOPE_ENUM.COMPANY)
```

## 📖 Complete Controller Example

```typescript
import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
} from '@nestjs/common';
import { PermissionGuard } from '../auth/guards/permission.guard';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
  Permission,
} from '../auth/decorators/permission.decorator';
import {
  PLATFORM_SECTION_ENUM,
  PERMISSION_ACTION_ENUM,
  ROLE_SCOPE_ENUM,
} from '../../../utils/enums';

@Controller('employees')
@UseGuards(PermissionGuard)
export class EmployeesController {
  // Company scope operations
  @Get()
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES, ROLE_SCOPE_ENUM.COMPANY)
  findCompanyEmployees() {
    return this.employeesService.findCompanyEmployees();
  }

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES, ROLE_SCOPE_ENUM.COMPANY)
  create(@Body() createEmployeeDto: CreateEmployeeDto) {
    return this.employeesService.create(createEmployeeDto);
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES, ROLE_SCOPE_ENUM.COMPANY)
  update(
    @Param('id') id: string,
    @Body() updateEmployeeDto: UpdateEmployeeDto,
  ) {
    return this.employeesService.update(id, updateEmployeeDto);
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.EMPLOYEES, ROLE_SCOPE_ENUM.COMPANY)
  remove(@Param('id') id: string) {
    return this.employeesService.remove(id);
  }

  // Company-only operations
  @Post(':id/assign-role')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES, ROLE_SCOPE_ENUM.COMPANY)
  assignRole(@Param('id') id: string, @Body() roleData: any) {
    return this.employeesService.assignRole(id, roleData);
  }

  // Platform-only operations (admin)
  @Get('admin/all-companies')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES, ROLE_SCOPE_ENUM.PLATFORM)
  getAllFromAllCompanies() {
    return this.employeesService.findAllFromAllCompanies();
  }

  // Custom permission with full control
  @Post('bulk-import')
  @Permission(PLATFORM_SECTION_ENUM.EMPLOYEES, PERMISSION_ACTION_ENUM.CREATE, {
    scope: ROLE_SCOPE_ENUM.COMPANY,
  })
  bulkImport(@Body() importData: any) {
    return this.employeesService.bulkImport(importData);
  }
}
```

## 🏗️ Available Platform Sections

```typescript
PLATFORM_SECTION_ENUM.DASHBOARD;
PLATFORM_SECTION_ENUM.USERS;
PLATFORM_SECTION_ENUM.EMPLOYEES;
PLATFORM_SECTION_ENUM.TIME_ATTENDANCE;
PLATFORM_SECTION_ENUM.PAYROLLS;
PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT;
PLATFORM_SECTION_ENUM.DOCUMENTS;
PLATFORM_SECTION_ENUM.PERFORMANCE;
PLATFORM_SECTION_ENUM.PROJECT_BONUSES;
PLATFORM_SECTION_ENUM.CHAT;
PLATFORM_SECTION_ENUM.AI_ASSISTANCE;
PLATFORM_SECTION_ENUM.SETTINGS;
PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS;
```

## 🔧 Advanced Usage

### 1. Company vs Platform Operations

```typescript
// Company users can only access their company data
@Get('company-data')
@CanView(PLATFORM_SECTION_ENUM.DASHBOARD, ROLE_SCOPE_ENUM.COMPANY)
getCompanyData() {
  // Returns only company-specific data
}

// Platform admins can access all data
@Get('admin/all-data')
@CanView(PLATFORM_SECTION_ENUM.DASHBOARD, ROLE_SCOPE_ENUM.PLATFORM)
getAllData() {
  // Returns data from all companies
}
```

### 2. Complex Business Logic

```typescript
@Post('payroll/calculate')
@Permission(
  PLATFORM_SECTION_ENUM.PAYROLLS,
  PERMISSION_ACTION_ENUM.CREATE,
  { scope: ROLE_SCOPE_ENUM.COMPANY } // Only company users can calculate payroll
)
calculatePayroll(@Body() payrollData: any) {
  return this.payrollService.calculate(payrollData);
}
```

### 3. Public Routes (No Permission Required)

```typescript
@Get('public/health')
healthCheck() {
  return { status: 'OK' };
}
```

## 🧪 Testing Permissions

### Check User Permissions Endpoint

```http
GET /permissions/my-permissions
Authorization: Bearer <jwt-token>
```

### Check Specific Permission

```http
POST /permissions/check
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "section": "users",
  "action": "canView",
  "companyId": 1
}
```

## 🛡️ Best Practices

### 1. Use Explicit Scopes

```typescript
// ✅ Good - explicit scope
@CanCreate(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)

// ❌ Avoid - scope is required
@CanCreate(PLATFORM_SECTION_ENUM.USERS)
```

### 2. Consistent Controller Structure

```typescript
@Controller('users')
@UseGuards(PermissionGuard) // Always use at controller level
export class UsersController {
  // Group by scope for clarity

  // Company operations
  @Get()
  @CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)
  findCompanyUsers() {}

  // Platform operations
  @Get('admin/all')
  @CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.PLATFORM)
  findAllUsers() {}
}
```

### 3. Error Handling

The permission system provides clear error messages:

```json
// 401 Unauthorized
{
  "statusCode": 401,
  "message": "Authentication required"
}

// 403 Forbidden
{
  "statusCode": 403,
  "message": "Insufficient permissions for users:canView"
}

// 403 Company Scope Error
{
  "statusCode": 403,
  "message": "User does not belong to any company"
}

// 403 Platform Scope Error
{
  "statusCode": 403,
  "message": "Platform scope required for this operation"
}
```

## 🔄 Backward Compatibility

The old decorators still work but are **deprecated**:

```typescript
// Still works but deprecated
@RequireViewPermission(PLATFORM_SECTION_ENUM.USERS)
@RequireCreatePermission(PLATFORM_SECTION_ENUM.USERS)
@RequirePlatformViewPermission(PLATFORM_SECTION_ENUM.USERS)

// Recommended new syntax
@CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)
@CanCreate(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.COMPANY)
@CanView(PLATFORM_SECTION_ENUM.USERS, ROLE_SCOPE_ENUM.PLATFORM)
```

## 🎉 Benefits of Simplified System

1. **Fewer Decorators**: 2 main decorators instead of 8
2. **Cleaner Syntax**: More intuitive and readable
3. **Better TypeScript Support**: Enhanced type safety
4. **Easier Maintenance**: Less code to maintain
5. **Backward Compatible**: Existing code continues to work
6. **Consistent API**: Unified approach across all operations
7. **Clear Scope Separation**: Explicit PLATFORM vs COMPANY scopes

## 🔍 CURL Testing Examples

### Basic Operations

```bash
# View users (company scope)
curl -X GET "{{base_url}}/users" \
  -H "Authorization: Bearer <company-user-token>"

# Create user (company scope)
curl -X POST "{{base_url}}/users" \
  -H "Authorization: Bearer <company-user-token>" \
  -H "Content-Type: application/json" \
  -d '{"name": "John Doe", "email": "<EMAIL>"}'

# Admin view all users (platform scope)
curl -X GET "{{base_url}/admin/users" \
  -H "Authorization: Bearer <platform-admin-token>"
```

### Permission Testing

```bash
# Check user permissions
curl -X GET "{{base_url}}/permissions/my-permissions" \
  -H "Authorization: Bearer <jwt-token>"

# Test specific permission
curl -X POST "{{base_url}}/permissions/check" \
  -H "Authorization: Bearer <jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{"section": "users", "action": "canView", "companyId": 1}'
```

---

## 🚀 Getting Started

1. **Update your controllers** to use the new decorators
2. **Test with different user roles** to ensure permissions work correctly
3. **Gradually migrate** from old decorators to new ones
4. **Use the simplified syntax** for all new code

The simplified system maintains all the power of the original while being much easier to use and maintain!
