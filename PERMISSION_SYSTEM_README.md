# Simplified Dynamic Permission System

This document describes the **simplified** implementation of a comprehensive, dynamic permission system for the HR API using NestJS, PostgreSQL, and TypeScript.

## 🎯 What's New - Simplified Architecture

The permission system has been **dramatically simplified** while maintaining all functionality:

### Before vs After

| Before                        | After                          | Improvement      |
| ----------------------------- | ------------------------------ | ---------------- |
| 8 different decorators        | 2 main decorators              | 75% reduction    |
| Complex auto-detection logic  | Simple, explicit permissions   | Cleaner code     |
| Redundant admin controllers   | Unified controllers with scope | Less duplication |
| Confusing interface structure | Clear, typed interfaces        | Better DX        |

## 🏗️ Architecture Overview

The permission system is built around these core concepts:

- **Roles**: Define user roles (e.g., Super Admin, Company Admin, Employee)
- **Platform Sections**: Define different areas of the application (e.g., Users, Employees, Payrolls)
- **Permissions**: Define what actions users can perform on each section (View, Create, Edit, Delete)
- **Scopes**: Define the scope of permissions (Platform, Company, or Both)

## 🚀 New Simplified Decorators

### Main Decorators

#### 1. `@Permission()` - Universal Decorator

```typescript
@Permission(section, action?, options?)
```

#### 2. CRUD Convenience Decorators

```typescript
@CanView(section, scope?)     // View permission
@CanCreate(section, scope?)   // Create permission
@CanEdit(section, scope?)     // Edit permission
@CanDelete(section, scope?)   // Delete permission
```

### Scope System

| Scope              | Description                            | Access               |
| ------------------ | -------------------------------------- | -------------------- |
| `'both'` (default) | Allows both company and platform users | Company + Platform   |
| `'company'`        | Company scope only                     | Company users only   |
| `'platform'`       | Platform scope only                    | Platform admins only |

## 📖 Usage Examples

### Basic Controller Setup

```typescript
import { Controller, Get, Post, UseGuards } from '@nestjs/common';
import { PermissionGuard } from '../auth/guards/permission.guard';
import { CanView, CanCreate } from '../auth/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from '../../../utils/enums';

@Controller('users')
@UseGuards(PermissionGuard)
export class UsersController {
  // Both company and platform users can view
  @Get()
  @CanView(PLATFORM_SECTION_ENUM.USERS)
  findAll() {
    return this.usersService.findAll();
  }

  // Only company users can create
  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.USERS, 'company')
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  // Only platform admins can access
  @Get('admin/all')
  @CanView(PLATFORM_SECTION_ENUM.USERS, 'platform')
  findAllFromAllCompanies() {
    return this.usersService.findAllFromAllCompanies();
  }
}
```

### Advanced Usage

```typescript
// Custom permission with full control
@Post('bulk-operations')
@Permission(
  PLATFORM_SECTION_ENUM.USERS,
  PERMISSION_ACTION_ENUM.CREATE,
  { scope: 'company' }
)
bulkCreate(@Body() data: any[]) {
  return this.usersService.bulkCreate(data);
}

// Mixed scope - platform admins see all, company users see their data
@Get('reports')
@CanView(PLATFORM_SECTION_ENUM.DASHBOARD, 'both')
getReports() {
  // Logic handles scope automatically
  return this.reportsService.getReports();
}
```

## 🔧 Database Models

### Core Models (Unchanged)

1. **Role Model** (`src/models/roles-and-permissions/role.model.ts`)

   - Defines user roles with scope (Platform or Company)
   - Links to company for company-specific roles

2. **Platform Section Model** (`src/models/roles-and-permissions/platform-section.model.ts`)

   - Defines different sections of the platform
   - Examples: Dashboard, Users, Employees, Payrolls, etc.

3. **Permission Model** (`src/models/roles-and-permissions/permission.model.ts`)

   - Links roles to platform sections
   - Defines CRUD permissions (canView, canCreate, canEdit, canDelete)

4. **User Role Model** (`src/models/roles-and-permissions/user-role.model.ts`)
   - Links users to their assigned roles

## 🛠️ Core Components

### 1. Simplified Enums (`src/utils/enums.ts`)

```typescript
// Permission Actions
export enum PERMISSION_ACTION_ENUM {
  VIEW = 'canView',
  CREATE = 'canCreate',
  EDIT = 'canEdit',
  DELETE = 'canDelete',
}

// Platform Sections
export enum PLATFORM_SECTION_ENUM {
  DASHBOARD = 'dashboard',
  USERS = 'users',
  EMPLOYEES = 'employees',
  TIME_ATTENDANCE = 'time-attendance',
  PAYROLLS = 'payrolls',
  LEAVE_MANAGEMENT = 'leave-management',
  DOCUMENTS = 'documents',
  PERFORMANCE = 'performance',
  PROJECT_BONUSES = 'project-bonuses',
  CHAT = 'chat',
  AI_ASSISTANCE = 'ai-assistance',
  SETTINGS = 'settings',
  ROLES_AND_PERMISSIONS = 'roles-and-permissions',
}
```

### 2. Simplified Permission Guard (`src/modules/auth/guards/permission.guard.ts`)

The guard has been simplified to remove complex auto-detection logic:

- ✅ **Explicit permissions only** - No more guessing
- ✅ **Cleaner error handling** - Clear error messages
- ✅ **Better performance** - Fewer database queries
- ✅ **Easier debugging** - Transparent permission logic

### 3. Enhanced Permission Service (`src/modules/roles-and-permissions/permissions/permission.service.ts`)

Core service maintains the same functionality with improved:

- Optimized permission checking
- Better caching strategies
- Simplified company scope validation
- Enhanced platform scope detection

## 🔄 Migration Guide

### Decorator Migration

```typescript
// OLD (8 decorators)
@RequireViewPermission(PLATFORM_SECTION_ENUM.USERS)
@RequireCreatePermission(PLATFORM_SECTION_ENUM.USERS)
@RequireEditPermission(PLATFORM_SECTION_ENUM.USERS)
@RequireDeletePermission(PLATFORM_SECTION_ENUM.USERS)
@RequirePlatformViewPermission(PLATFORM_SECTION_ENUM.USERS)
@RequirePlatformCreatePermission(PLATFORM_SECTION_ENUM.USERS)
@RequirePlatformEditPermission(PLATFORM_SECTION_ENUM.USERS)
@RequirePlatformDeletePermission(PLATFORM_SECTION_ENUM.USERS)

// NEW (2 main decorators)
@CanView(PLATFORM_SECTION_ENUM.USERS)                    // Company + Platform
@CanCreate(PLATFORM_SECTION_ENUM.USERS)                  // Company + Platform
@CanEdit(PLATFORM_SECTION_ENUM.USERS)                    // Company + Platform
@CanDelete(PLATFORM_SECTION_ENUM.USERS)                  // Company + Platform
@CanView(PLATFORM_SECTION_ENUM.USERS, 'platform')       // Platform only
@CanCreate(PLATFORM_SECTION_ENUM.USERS, 'platform')     // Platform only
@CanEdit(PLATFORM_SECTION_ENUM.USERS, 'platform')       // Platform only
@CanDelete(PLATFORM_SECTION_ENUM.USERS, 'platform')     // Platform only
```

### Controller Migration

```typescript
// OLD - Separate admin controllers
@Controller('admin/users')
export class AdminUsersController {
  @Get()
  @RequirePlatformViewPermission(PLATFORM_SECTION_ENUM.USERS)
  findAll() {}
}

@Controller('users')
export class UsersController {
  @Get()
  @RequireViewPermission(PLATFORM_SECTION_ENUM.USERS)
  findAll() {}
}

// NEW - Unified controller
@Controller('users')
export class UsersController {
  @Get()
  @CanView(PLATFORM_SECTION_ENUM.USERS, 'company')
  findCompanyUsers() {}

  @Get('admin/all')
  @CanView(PLATFORM_SECTION_ENUM.USERS, 'platform')
  findAllUsers() {}
}
```

## 🧪 API Endpoints

### Permission Management (Unchanged)

```http
GET /permissions/my-permissions
GET /permissions/accessible-sections
POST /permissions/check
POST /permissions/check-multiple
GET /permissions/company-id
GET /permissions/platform-scope
```

## 🔒 Security Features

1. **JWT Token Validation**: All protected routes require valid JWT tokens
2. **Scope-Based Access**: Automatic scope detection and validation
3. **Company Isolation**: Company users can only access their data
4. **Platform Privileges**: Platform admins have cross-company access
5. **Fine-Grained Permissions**: CRUD-level permission control
6. **Dynamic Validation**: Runtime permission checking

## 🚀 Performance Improvements

1. **Reduced Complexity**: Simpler guard logic = faster execution
2. **Optimized Database Queries**: Streamlined permission checking
3. **Better Caching**: Improved permission caching strategies
4. **Fewer Decorators**: Less metadata processing overhead

## 📊 Benefits Summary

### Developer Experience

- **75% fewer decorators** to remember
- **Cleaner, more intuitive syntax**
- **Better TypeScript support**
- **Simplified testing**

### Maintainability

- **Less code to maintain**
- **Unified permission logic**
- **Easier to debug**
- **Better error messages**

### Performance

- **Faster guard execution**
- **Optimized database queries**
- **Reduced memory overhead**
- **Better caching**

## 🔧 Configuration

### Environment Variables (if needed)

```env
# Permission system settings
PERMISSION_CACHE_TTL=300
PERMISSION_DEBUG=false
```

### Module Setup

```typescript
@Module({
  imports: [
    SequelizeModule.forFeature([
      UserRole,
      Role,
      Permission,
      PlatformSection,
      Company,
    ]),
  ],
  controllers: [PermissionController],
  providers: [PermissionService, PermissionGuard],
  exports: [PermissionService, PermissionGuard],
})
export class PermissionModule {}
```

## 🧪 Testing

### Unit Tests

```typescript
describe('PermissionGuard', () => {
  it('should allow access with correct permissions', async () => {
    // Test implementation
  });

  it('should deny access without permissions', async () => {
    // Test implementation
  });
});
```

### Integration Tests

```typescript
describe('Permission System Integration', () => {
  it('should work end-to-end', async () => {
    // Test with real database
  });
});
```

## 🚀 CURL Examples

### Basic Operations

```bash
# Company user accessing their data
curl -X GET "{{base_url}}/users" \
  -H "Authorization: Bearer <company-user-token>"

# Platform admin accessing all data
curl -X GET "{{base_url}}/users/admin/all" \
  -H "Authorization: Bearer <platform-admin-token>"

# Creating a new user (company scope)
curl -X POST "{{base_url}}/users" \
  -H "Authorization: Bearer <company-user-token>" \
  -H "Content-Type: application/json" \
  -d '{"name": "John Doe", "email": "<EMAIL>"}'
```

### Permission Testing

```bash
# Check user permissions
curl -X GET "{{base_url}}/permissions/my-permissions" \
  -H "Authorization: Bearer <jwt-token>"

# Test specific permission
curl -X POST "{{base_url}}/permissions/check" \
  -H "Authorization: Bearer <jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{"section": "users", "action": "canView", "companyId": 1}'
```

## 📝 Best Practices

### 1. Consistent Naming

```typescript
// ✅ Good
@CanView(PLATFORM_SECTION_ENUM.USERS, 'company')

// ❌ Avoid
@RequireViewPermission(PLATFORM_SECTION_ENUM.USERS, true)
```

### 2. Explicit Scopes

```typescript
// ✅ Good - explicit scope
@CanCreate(PLATFORM_SECTION_ENUM.USERS, 'company')

// ❌ Avoid - implicit scope
@CanCreate(PLATFORM_SECTION_ENUM.USERS)
```

### 3. Logical Grouping

```typescript
@Controller('users')
export class UsersController {
  // Company operations
  @Get()
  @CanView(PLATFORM_SECTION_ENUM.USERS, 'company')
  findCompanyUsers() {}

  // Platform operations
  @Get('admin/all')
  @CanView(PLATFORM_SECTION_ENUM.USERS, 'platform')
  findAllUsers() {}
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Permission Denied**: Check user role and permissions
2. **Company Scope Error**: Verify user belongs to company
3. **Platform Scope Error**: Ensure user has platform permissions

### Debug Commands

```bash
# Check user permissions
GET /permissions/my-permissions

# Check accessible sections
GET /permissions/accessible-sections

# Verify company ID
GET /permissions/company-id
```

## 🔮 Future Enhancements

1. **Permission Caching**: Redis-based permission caching
2. **Audit Logging**: Track permission checks and changes
3. **Role Templates**: Predefined role templates
4. **Dynamic Permissions**: Runtime permission modifications

## 🎉 Conclusion

The simplified permission system provides:

- **Dramatically reduced complexity** (75% fewer decorators)
- **Maintained functionality** (all features preserved)
- **Better developer experience** (cleaner syntax)
- **Improved performance** (optimized guard logic)
- **Enhanced maintainability** (less code to maintain)

This system is designed to be:

- **Simple** to use and understand
- **Powerful** enough for complex scenarios
- **Scalable** for growing applications
- **Maintainable** for long-term projects

The permission system successfully balances **simplicity** with **functionality**, making it easier for developers to implement secure, role-based access control in their NestJS applications.
