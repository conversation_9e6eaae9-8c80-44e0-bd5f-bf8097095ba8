# Simplified Permission System

## Overview

This project uses a clean, simplified permission system with clear separation between platform and company scopes.

## Permission Decorators

### Company-Scoped Permissions (Default)

These are for regular company users and operations within a company context:

```typescript
@CanView('users')           // View users in company
@CanCreate('users')         // Create users in company
@CanEdit('users')           // Edit users in company
@CanDelete('users')         // Delete users in company
```

### Platform-Scoped Permissions (Admin Only)

These are for platform administrators who can access all companies:

```typescript
@CanViewPlatform('users')     // View all users across platform
@CanCreatePlatform('users')   // Create users across platform
@CanEditPlatform('users')     // Edit users across platform
@CanDeletePlatform('users')   // Delete users across platform
```

### Custom Permissions

For specific requirements:

```typescript
@RequirePermission('users', 'canView', ROLE_SCOPE_ENUM.COMPANY)
@RequirePermission('users', 'canCreate', ROLE_SCOPE_ENUM.PLATFORM)
```

## How It Works

### 1. Platform Users (Super Admin)

- Can access **ALL** endpoints (both platform and company scoped)
- Have `ROLE_SCOPE_ENUM.PLATFORM` scope
- Bypass company restrictions

### 2. Company Users

- Can only access company-scoped endpoints
- Must belong to a company
- Have `ROLE_SCOPE_ENUM.COMPANY` scope
- Permissions are checked against their role

### 3. Permission Guard Logic

```
1. Check if user has platform scope → Grant access to everything
2. If platform-scoped endpoint → Deny access (only platform users allowed)
3. If company-scoped endpoint → Check company permissions
```

## Usage Examples

### Controller Setup

```typescript
@Controller('users')
@UseGuards(PermissionGuard)  // Apply to all routes
export class UsersController {

  @Get()
  @CanView(PLATFORM_SECTION_ENUM.USERS)  // Company-scoped
  findAll() { ... }

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.USERS)  // Company-scoped
  create() { ... }

  @Get('admin/all')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.USERS)  // Platform-scoped
  findAllPlatform() { ... }
}
```

### Service Integration

Controllers handle permissions, services handle business logic:

```typescript
// Controller
@CanView(PLATFORM_SECTION_ENUM.USERS)
findAll(@Req() req: UserRequestI) {
  return this.usersService.findByCompany(req.user.id);
}

// Service
async findByCompany(userId: number) {
  // Business logic - no permission checks needed here
  const company = await this.getUserCompany(userId);
  return this.userModel.findAll({ where: { companyId: company.id } });
}
```

## Platform Sections

Defined in `PLATFORM_SECTION_ENUM`:

- `USERS`
- `ROLES_AND_PERMISSIONS`
- `EMPLOYEES`
- `COMPANIES`
- etc.

## Migration from Old System

Replace old decorators:

- `@RequireViewPermission()` → `@CanView()`
- `@RequireCreatePermission()` → `@CanCreate()`
- `@RequireEditPermission()` → `@CanEdit()`
- `@RequireDeletePermission()` → `@CanDelete()`
- `@RequirePlatformViewPermission()` → `@CanViewPlatform()`

## Benefits

1. **Clear & Simple**: Easy to understand platform vs company scope
2. **No Duplication**: Single source of truth for permissions
3. **Consistent**: Same pattern across all controllers
4. **Secure**: Platform users can access everything, company users are restricted
5. **Maintainable**: Less code, fewer bugs
