import { Injectable, NotFoundException } from '@nestjs/common';
import {
  Model,
  FindOptions,
  UpdateOptions,
  DestroyOptions,
  WhereOptions,
} from 'sequelize';

type SequelizeModel<T extends Model> = {
  new (...args: any[]): T;
  findAll(options?: FindOptions): Promise<T[]>;
  findOne(options?: FindOptions): Promise<T | null>;
  create(values?: object): Promise<T>;
  update(values: object, options: UpdateOptions): Promise<[number, T[]]>;
  destroy(options?: DestroyOptions): Promise<number>;
  findAndCountAll(options?: FindOptions): Promise<{ count: number; rows: T[] }>;
};

interface PaginationOptions {
  page?: number;
  limit?: number;
  where?: WhereOptions;
  order?: any[];
  include?: any;
  attributes?: string[];
}

export interface PaginatedResult<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    lastPage: number;
    limit: number;
    totalRecords?: number;
  };
}

@Injectable()
export class CrudHelperService {
  async create<T extends Model>(
    model: SequelizeModel<T>,
    data: Partial<T['_creationAttributes']>,
  ): Promise<Partial<T>> {
    const record = await model.create(data);
    return record.get({ plain: true });
  }

  async findAll<T extends Model>(
    model: SequelizeModel<T>,
    options?: FindOptions,
  ): Promise<T[]> {
    // If includes are present, don't use raw: true to preserve associations
    const hasIncludes =
      options?.include &&
      (Array.isArray(options.include) ? options.include.length > 0 : true);
    const shouldUseRaw = !hasIncludes;

    return model.findAll({
      ...options,
      raw: shouldUseRaw,
      nest: shouldUseRaw,
    });
  }

  async findOne<T extends Model>(
    model: SequelizeModel<T>,
    options: FindOptions,
  ): Promise<Partial<T>> {
    // If includes are present, don't use raw: true to preserve associations
    const hasIncludes =
      options.include &&
      (Array.isArray(options.include) ? options.include.length > 0 : true);
    const shouldUseRaw = !hasIncludes;

    const record = await model.findOne({
      ...options,
      raw: shouldUseRaw,
      nest: shouldUseRaw,
    });

    return record;
  }

  async update<T extends Model>(
    model: SequelizeModel<T>,
    data: Partial<T['_creationAttributes']>,
    options: UpdateOptions,
  ): Promise<Partial<T>> {
    const [affectedRows] = await model.update(data, options);
    if (affectedRows === 0)
      throw new NotFoundException('Update failed: Record not found');

    if (options.where) {
      const updatedRecord = await model.findOne({
        where: options.where,
        raw: true,
        nest: true,
      });

      if (!updatedRecord)
        throw new NotFoundException('Updated record not found');

      return updatedRecord;
    }

    return data as unknown as Partial<T>;
  }

  async delete<T extends Model>(
    model: SequelizeModel<T>,
    options: DestroyOptions,
  ): Promise<void> {
    const deletedCount = await model.destroy(options);
    if (deletedCount === 0)
      throw new NotFoundException('Delete failed: Record not found');
  }

  async paginateAll<T extends Model>(
    model: SequelizeModel<T>,
    page = 1,
    limit = 10,
  ): Promise<PaginatedResult<Partial<T>>> {
    const offset = (page - 1) * limit;
    const { count, rows } = await model.findAndCountAll({
      limit,
      offset,
      raw: true,
      nest: true,
    });

    return {
      data: rows,
      meta: {
        total: count,
        page,
        lastPage: Math.ceil(count / limit),
        limit,
      },
    };
  }

  async paginateWithQuery<T extends Model>(
    model: SequelizeModel<T>,
    {
      page = 1,
      limit = 10,
      where = {},
      order = [['createdAt', 'DESC']],
      include = [],
      attributes, // ✅ NEW
    }: PaginationOptions,
  ): Promise<PaginatedResult<Partial<T>>> {
    const offset = (page - 1) * limit;

    // If includes are present, don't use raw: true to preserve associations
    const hasIncludes =
      include && (Array.isArray(include) ? include.length > 0 : true);
    const shouldUseRaw = !hasIncludes;

    const { count, rows } = await model.findAndCountAll({
      where,
      limit,
      offset,
      order,
      include,
      raw: shouldUseRaw,
      nest: shouldUseRaw,
    });

    return {
      data: rows,
      meta: {
        total: count,
        page,
        lastPage: Math.ceil(count / limit),
        limit,
        totalRecords: count,
      },
    };
  }
}
