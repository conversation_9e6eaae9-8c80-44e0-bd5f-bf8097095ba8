import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ValidationError } from 'sequelize';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // Handle different types of errors
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message: string | object = 'Internal server error';
    let errorDetails: any = null;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const response = exception.getResponse();
      message =
        typeof response === 'string'
          ? response
          : response['message'] || response;
      errorDetails = response;
    } else if (exception instanceof ValidationError) {
      // Handle Sequelize validation errors
      status = HttpStatus.BAD_REQUEST;
      message = 'Validation error';
      errorDetails = exception.errors.map((err) => ({
        field: err.path,
        message: err.message,
        value: err.value,
      }));
    } else if (exception instanceof Error) {
      // Handle other Error instances
      message = exception.message;
      errorDetails = {
        name: exception.name,
        stack:
          process.env.NODE_ENV === 'development' ? exception.stack : undefined,
      };
    }

    // Log the error
    this.logger.error(
      `[${request.method}] ${request.url} - ${status}`,
      {
        timestamp: new Date().toISOString(),
        path: request.url,
        method: request.method,
        error: {
          message,
          details: errorDetails,
        },
        body: request.body,
        query: request.query,
        params: request.params,
      },
      exception instanceof Error ? exception.stack : undefined,
    );

    // Send response
    response.status(status).json({
      success: false,
      message,
      data: null,
      errors: errorDetails,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}
