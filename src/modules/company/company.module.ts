import { Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Company } from '../../models/company.model';
import { CompanyController } from './controllers/company.controller';
import { CompanyService } from './services/company.service';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Company, EmploymentDetails]),
    forwardRef(() => UsersModule),
  ],
  controllers: [CompanyController],
  providers: [CompanyService],
  exports: [CompanyService],
})
export class CompanyModule {}
