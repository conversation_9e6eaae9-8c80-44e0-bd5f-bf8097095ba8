import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Company } from '../../../models/company.model';
import { CreateCompanyDto } from '../dto/create-company.dto';
import { UpdateCompanyDto } from '../dto/update-company.dto';
import { CrudHelperService } from 'src/common/crud-helper/crud-helper.service';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { RedirectSection } from 'src/utils/redirect-section.enum';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { UsersService } from 'src/modules/users/services/users.service';

@Injectable()
export class CompanyService {
  constructor(
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(EmploymentDetails)
    private readonly employmentDetailsModel: typeof EmploymentDetails,
    private readonly crudHelperService: CrudHelperService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    user: RequestUserObjectI,
    createCompanyDto: CreateCompanyDto,
  ): Promise<Partial<Company>> {
    const [existingName, existingPortalName] = await Promise.all([
      this.crudHelperService.findOne(this.companyModel, {
        where: { name: createCompanyDto.name },
      }),
      this.crudHelperService.findOne(this.companyModel, {
        where: { portalName: createCompanyDto.portalName },
      }),
    ]);

    if (existingName) {
      throw new ConflictException('Company with this name already exists');
    }

    if (existingPortalName) {
      throw new ConflictException(
        'Company with this portal name already exists',
      );
    }

    const company = await this.crudHelperService.create<Company>(
      this.companyModel,
      {
        name: createCompanyDto.name,
        portalName: createCompanyDto.portalName,
        industry: createCompanyDto.industry,
        website: createCompanyDto.website,
        email: createCompanyDto.email,
      },
    );

    await this.usersService.updateUser(user.id, {
      redirectTo: RedirectSection.DASHBOARD,
    });

    return company;
  }

  async checkPortalNameUnique(portalName: string): Promise<boolean> {
    const existingPortalName = await this.crudHelperService.findOne(
      this.companyModel,
      {
        where: { portalName },
      },
    );
    return !existingPortalName;
  }

  async findAll(): Promise<Partial<Company>[]> {
    return this.crudHelperService.findAll<Company>(this.companyModel, {
      include: ['address', 'contactDetails'],
    });
  }

  async findOne(id: number): Promise<Partial<Company>> {
    const company = await this.crudHelperService.findOne<Company>(
      this.companyModel,
      {
        where: { id },
        include: ['address', 'contactDetails'],
      },
    );

    if (!company) {
      throw new NotFoundException(`Company with ID ${id} not found`);
    }

    return company;
  }

  async update(
    id: number,
    updateCompanyDto: UpdateCompanyDto,
  ): Promise<Partial<Company>> {
    await this.findOne(id);

    if (updateCompanyDto.name) {
      const existingName = await this.companyModel.findOne({
        where: { name: updateCompanyDto.name },
      });
      if (existingName && existingName.id !== id) {
        throw new ConflictException('Company with this name already exists');
      }
    }

    if (updateCompanyDto.portalName) {
      const existingPortalName = await this.companyModel.findOne({
        where: { portalName: updateCompanyDto.portalName },
      });
      if (existingPortalName && existingPortalName.id !== id) {
        throw new ConflictException(
          'Company with this portal name already exists',
        );
      }
    }

    return this.crudHelperService.update<Company>(
      this.companyModel,
      updateCompanyDto,
      {
        where: { id },
      },
    );
  }

  async remove(id: number): Promise<void> {
    await this.crudHelperService.delete(this.companyModel, {
      where: { id },
    });
  }

  async findByUserId(userId: number): Promise<Partial<Company>> {
    const employmentDetails =
      await this.crudHelperService.findOne<EmploymentDetails>(
        this.employmentDetailsModel,
        {
          where: { userId },
        },
      );

    if (!employmentDetails) {
      throw new NotFoundException(
        `Employment details not found for user ${userId}`,
      );
    }

    return this.crudHelperService.findOne<Company>(this.companyModel, {
      where: { id: employmentDetails.companyId },
    });
  }
}
