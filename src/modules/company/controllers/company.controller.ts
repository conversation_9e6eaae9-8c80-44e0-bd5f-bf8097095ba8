import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
} from '@nestjs/common';
import { CompanyService } from '../services/company.service';
import { CreateCompanyDto } from '../dto/create-company.dto';
import { UpdateCompanyDto } from '../dto/update-company.dto';
import { Company } from '../../../models/company.model';
import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth.guard';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';

@Controller('companies')
@UseGuards(JwtAuthGuard)
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}

  @Post()
  async create(
    @Req() req: UserRequestI,
    @Body() createCompanyDto: CreateCompanyDto,
  ): Promise<Partial<Company>> {
    const user = req.user;
    return this.companyService.create(user, createCompanyDto);
  }

  @Get('check-portal-name-unique/:portalName')
  async checkPortalNameUnique(
    @Param('portalName') portalName: string,
  ): Promise<boolean> {
    return this.companyService.checkPortalNameUnique(portalName);
  }

  @Get()
  async findAll(): Promise<Partial<Company>[]> {
    return this.companyService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Partial<Company>> {
    return this.companyService.findOne(+id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateCompanyDto: UpdateCompanyDto,
  ): Promise<Partial<Company>> {
    return this.companyService.update(+id, updateCompanyDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<void> {
    return this.companyService.remove(+id);
  }
}
