import { Global, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from '../../models/users-models/user.model';
import { UsersController } from './controllers/users.controller';
import { UsersService } from './services/users.service';
import { UserRoleModule } from '../roles-and-permissions/user-role/user-role.module';
import { UserDetails } from 'src/models/users-models/user-details.model';
import { AdminUserRoleController } from '../roles-and-permissions/user-role/controllers/admin.user.role.controller';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { AuthModule } from '../auth/auth.module';
import { AdminUsersController } from './controllers/admin.users.controller';
import { PermissionsModule } from '../roles-and-permissions/permissions/permissions.module';

@Global()
@Module({
  imports: [
    SequelizeModule.forFeature([User, UserDetails, EmploymentDetails]),
    UserRoleModule,
    PermissionsModule,
  ],
  controllers: [UsersController, AdminUserRoleController, AdminUsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
