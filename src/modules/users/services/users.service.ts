import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { User } from 'src/models/users-models/user.model';
import { CreateUserDto, UpdateUserDto } from '../dto';
import * as bcrypt from 'bcrypt';
import { UserRoleService } from 'src/modules/roles-and-permissions/user-role/user-role.service';
import { RedirectSection } from 'src/utils/redirect-section.enum';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { AuthService } from 'src/modules/auth/services/auth.service';
import { ROLES_ENUM } from 'src/modules/roles-and-permissions/roles/utils/enums';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import {
  AdminRequestI,
  RequestUserObjectI,
} from 'src/modules/auth/auth.interfaces';
import { Sequelize } from 'sequelize-typescript';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(EmploymentDetails)
    private employeeDetailsModel: typeof EmploymentDetails,
    private readonly crudHelperService: CrudHelperService,
    private readonly userRoleService: UserRoleService,
    private readonly sequelize: Sequelize,
  ) {}

  //platform
  async findAll(): Promise<Partial<User>[]> {
    return this.crudHelperService.findAll<User>(this.userModel);
  }

  async findOne(id: number): Promise<Partial<User>> {
    return this.crudHelperService.findOne<User>(this.userModel, {
      where: { id },
    });
  }

  async findByUsername(username: string): Promise<Partial<User>> {
    return this.crudHelperService.findOne<User>(this.userModel, {
      where: { username },
    });
  }

  async findByEmail(email: string): Promise<Partial<User>> {
    return this.crudHelperService.findOne<User>(this.userModel, {
      where: { email },
    });
  }

  async remove(id: number): Promise<void> {
    await this.crudHelperService.delete(this.userModel, {
      where: { id },
    });
  }

  async createUser(
    createUserDto: CreateUserDto,
    currentUserId?: number,
  ): Promise<Partial<User>> {
    const [existingUsername, existingEmail] = await Promise.all([
      this.userModel.findOne({ where: { username: createUserDto.username } }),
      this.userModel.findOne({ where: { email: createUserDto.email } }),
    ]);

    if (existingUsername) {
      throw new ConflictException('Username already exists');
    }

    if (existingEmail) {
      throw new ConflictException('Email already exists');
    }

    if (!createUserDto.roleId) {
      throw new ConflictException('Role ID is required for user creation');
    }

    // TODO: Move this to environment variables (.env file)
    const rawPassword = createUserDto.password || 'Temp1234!';
    const hashedPassword = await this.hashPassword(rawPassword);

    const transaction = await this.sequelize.transaction();

    const createdUser = await this.userModel.create(
      {
        email: createUserDto.email,
        username: createUserDto.username,
        password: hashedPassword,
        firstName: createUserDto.firstName,
        lastName: createUserDto.lastName,
        redirectTo: RedirectSection.CREATE_COMPANY,
      },
      { transaction },
    );

    await this.userRoleService.assignRoleWithTransaction(
      createdUser.id,
      createUserDto.roleId,
      transaction,
    );

    const hasEmploymentFields =
      createUserDto.position &&
      createUserDto.departmentId &&
      createUserDto.hireDate;

    if (currentUserId && hasEmploymentFields) {
      const currentUserData = await this.employeeDetailsModel.findOne({
        where: { userId: currentUserId },
      });

      if (!currentUserData?.companyId) {
        throw new ConflictException('Company context is missing');
      }

      await this.employeeDetailsModel.create(
        {
          userId: createdUser.id,
          position: createUserDto.position,
          departmentId: createUserDto.departmentId,
          companyId: currentUserData.companyId,
          hireDate: createUserDto.hireDate,
        },
        { transaction },
      );
    }

    await transaction.commit();

    return createdUser;
  }

  async updateUser(
    id: number,
    updateUserDto: UpdateUserDto,
  ): Promise<Partial<User>> {
    await this.findOne(id);

    if (updateUserDto.password) {
      updateUserDto.password = await this.hashPassword(updateUserDto.password);
    }

    if (updateUserDto.username) {
      const existingUsername = await this.userModel.findOne({
        where: { username: updateUserDto.username },
      });
      if (existingUsername && existingUsername.id !== id) {
        throw new ConflictException('Username already exists');
      }
    }

    if (updateUserDto.email) {
      const existingEmail = await this.userModel.findOne({
        where: { email: updateUserDto.email },
      });
      if (existingEmail && existingEmail.id !== id) {
        throw new ConflictException('Email already exists');
      }
    }

    return this.crudHelperService.update<User>(this.userModel, updateUserDto, {
      where: { id },
    });
  }

  async getUsersPaginated(
    user: RequestUserObjectI,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResult<Partial<User>>> {
    const scope = user.scope;

    if (scope === ROLE_SCOPE_ENUM.PLATFORM)
      return this.getAllUsersByPlatformSuperAdminPaginated(page, limit);

    if (scope === ROLE_SCOPE_ENUM.COMPANY)
      return this.getUsersByCompanyPaginated(user.id, page, limit);

    throw new NotFoundException('Invalid scope');
  }

  async getAllUsersByPlatformSuperAdminPaginated(
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<User>>> {
    const result = await this.crudHelperService.paginateWithQuery<User>(
      this.userModel,
      {
        page,
        limit,
        attributes: [
          'id',
          'username',
          'email',
          'firstName',
          'lastName',
          'status',
          'profileImage',
        ],
        include: [
          {
            model: UserRole,
            include: [
              {
                model: Role,
                where: {
                  name: [
                    ROLES_ENUM.COMPANY_ADMIN,
                    ROLES_ENUM.COMPANY_SUPER_ADMIN,
                  ],
                },
              },
            ],
          },
          {
            model: EmploymentDetails,
            required: false,
            limit: 1,
          },
        ],
      },
    );

    // Transform employmentDetails from array to single object
    const transformedData = result.data.map((user) => {
      const userData = user.toJSON ? user.toJSON() : user;
      if (
        userData.employmentDetails &&
        Array.isArray(userData.employmentDetails)
      ) {
        userData.employmentDetails =
          userData.employmentDetails.length > 0
            ? userData.employmentDetails[0]
            : null;
      }
      return userData;
    });

    return {
      ...result,
      data: transformedData,
    };
  }

  async getUsersByCompanyPaginated(
    adminId: number,
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<User>>> {
    const adminEmployment =
      await this.crudHelperService.findOne<EmploymentDetails>(
        this.employeeDetailsModel,
        { where: { userId: adminId } },
      );

    if (!adminEmployment) {
      throw new NotFoundException('Admin employment details not found');
    }

    const result = await this.crudHelperService.paginateWithQuery<User>(
      this.userModel,
      {
        page,
        limit,
        attributes: [
          'id',
          'username',
          'email',
          'firstName',
          'lastName',
          'status',
          'profileImage',
        ],
        include: [
          {
            model: EmploymentDetails,
            where: { companyId: adminEmployment.companyId },
            limit: 1,
          },
          {
            model: UserRole,
            include: [
              {
                model: Role,
              },
            ],
          },
        ],
      },
    );

    // Transform employmentDetails from array to single object
    const transformedData = result.data.map((user) => {
      const userData = user.toJSON ? user.toJSON() : user;
      if (
        userData.employmentDetails &&
        Array.isArray(userData.employmentDetails)
      ) {
        userData.employmentDetails =
          userData.employmentDetails.length > 0
            ? userData.employmentDetails[0]
            : null;
      }
      return userData;
    });

    return {
      ...result,
      data: transformedData,
    };
  }

  // async getUsers(userId: number) {
  //   const userRole = await this.userRoleService.getRoleForUser(userId);
  //   console.log(userRole, 'userRole');
  //   if (!userRole) {
  //     throw new NotFoundException('User role not found');
  //   }
  //   const scope = userRole.role.scope;
  //   console.log(scope, 'scope');
  //   if (scope === SCOPE_ENUM.PLATFORM)
  //     return this.getAllUsersByPaltformSuperAdmin();
  //   if (scope === SCOPE_ENUM.COMPANY) return this.getUsersByCompany(userId);
  // }

  // // ... inside your service class ...

  // async getUsersByCompany(adminId: number): Promise<User[]> {
  //   const adminEmployment =
  //     await this.crudHelperService.findOne<EmploymentDetails>(
  //       this.employeeDetailsModel,
  //       {
  //         where: { userId: adminId },
  //       },
  //     );

  //   if (!adminEmployment) {
  //     throw new NotFoundException('Admin employment details not found');
  //   }

  //   const companyId = adminEmployment.companyId;

  //   const users = await this.crudHelperService.findAll<User>(this.userModel, {
  //     include: [
  //       {
  //         model: EmploymentDetails,
  //         where: { companyId },
  //         attributes: [], // skip selecting fields from the relation
  //       },
  //     ],
  //     attributes: [
  //       'id',
  //       'username',
  //       'email',
  //       'firstName',
  //       'lastName',
  //       'status',
  //       'profileImage',
  //     ],
  //   });

  //   return users;
  // }

  // async getAllUsersByPaltformSuperAdmin(): Promise<User[]> {
  //   const users = await this.crudHelperService.findAll<User>(this.userModel, {
  //     attributes: {
  //       exclude: [
  //         'password',
  //         'refreshToken',
  //         'redirectTo',
  //         'createdAt',
  //         'updatedAt',
  //       ],
  //     },
  //     include: [
  //       {
  //         model: UserRole,
  //         include: [
  //           {
  //             model: Role,
  //             where: {
  //               name: [
  //                 ROLES_ENUM.COMPANY_ADMIN,
  //                 ROLES_ENUM.COMPANY_SUPER_ADMIN,
  //               ],
  //             },
  //             // attributes: [],
  //           },
  //         ],
  //         attributes: [],
  //       },
  //     ],
  //   });

  //   return users;
  // }

  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }

  async validatePassword(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  async isAdmin(userId: number): Promise<boolean> {
    const admin = await this.userRoleService.findUserById(userId);
    return !!admin;
  }

  async storeUserRefreshToken(
    userId: number,
    refreshToken: string,
  ): Promise<void> {
    const user = await this.findOne(userId);
    if (!user) {
      throw new ConflictException('User not found');
    }

    const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);
    await this.updateUser(user.id, {
      refreshToken: hashedRefreshToken,
    });
  }

  async generateEmployeeId(): Promise<string> {
    const lastEmployee = await this.userModel.findOne({
      order: [['createdAt', 'DESC']],
    });

    const lastId = lastEmployee ? parseInt(lastEmployee.id.toString()) : 0;
    const newId = lastId + 1;
    return `EMP${newId.toString().padStart(3, '0')}`;
  }

  async getAllPaginated(
    page: number,
    limit: number,
    adminId: number,
  ): Promise<PaginatedResult<Partial<User>>> {
    const adminEmployment = await this.employeeDetailsModel.findOne({
      where: { userId: adminId },
    });

    if (!adminEmployment) {
      throw new NotFoundException('Admin employment record not found');
    }

    const companyId = adminEmployment.companyId;
    const offset = (page - 1) * limit;

    const { count, rows } = await this.userModel.findAndCountAll({
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: EmploymentDetails,
          where: { companyId },
          required: true,
          attributes: [],
        },
        {
          model: UserRole,
          attributes: ['id', 'userId', 'roleId', 'createdAt', 'updatedAt'],
          include: [
            {
              model: Role,
              attributes: ['id', 'name', 'description'],
            },
          ],
        },
      ],
      attributes: [
        'id',
        'username',
        'email',
        'firstName',
        'lastName',
        'status',
        'profileImage',
        'createdAt',
      ],
    });

    return {
      data: rows,
      meta: {
        total: count,
        page,
        lastPage: Math.ceil(count / limit),
        limit,
        totalRecords: count,
      },
    };
  }
}
