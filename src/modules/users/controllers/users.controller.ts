import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import { UsersService } from '../services/users.service';
import { CreateUserDto, UpdateUserDto } from '../dto';
import { User } from '../../../models/users-models/user.model';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { AdminRequestI, UserRequestI } from 'src/modules/auth/auth.interfaces';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from '../../../utils/enums';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth.guard';

@Controller('users')
@UseGuards(JwtAuthGuard)
// @UseGuards(PermissionGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('paginated')
  // @CanView(PLATFORM_SECTION_ENUM.USERS)
  findAllPaginated(
    @Req() req: UserRequestI,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<PaginatedResult<Partial<User>>> {
    const currentAdminId = req.user.id;
    return this.usersService.getAllPaginated(
      Number(page),
      Number(limit),
      currentAdminId,
    );
  }

  @Get(':id')
  // @CanView(PLATFORM_SECTION_ENUM.USERS)
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Partial<User>> {
    return this.usersService.findOne(id);
  }

  @Post()
  // @CanCreate(PLATFORM_SECTION_ENUM.USERS)
  create(@Body() createUserDto: CreateUserDto): Promise<Partial<User>> {
    return this.usersService.createUser(createUserDto);
  }

  @Patch(':id')
  // @CanEdit(PLATFORM_SECTION_ENUM.USERS)
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<Partial<User>> {
    return this.usersService.updateUser(id, updateUserDto);
  }

  @Delete(':id')
  // @CanDelete(PLATFORM_SECTION_ENUM.USERS)
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.usersService.remove(id);
  }

  @Post('onboarding')
  @CanCreate(PLATFORM_SECTION_ENUM.USERS)
  async createUser(
    @Body() createUserDto: CreateUserDto,
    @Req() req: AdminRequestI,
  ) {
    const currentAdminId = req.user.id;

    const createdEmployee = await this.usersService.createUser(
      createUserDto,
      currentAdminId,
    );

    return {
      message: 'User created successfully',
      data: createdEmployee,
    };
  }
}
