import {
  IsEmail,
  IsEnum,
  IsNot<PERSON>mpty,
  IsOptional,
  IsString,
  IsDateString,
  IsNumber,
  MinLength,
} from 'class-validator';

export class CreateUserDto {
  // User fields
  @IsNotEmpty()
  @IsString()
  username: string;

  @IsOptional()
  @IsString()
  password: string;

  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  lastName: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  redirectTo: string;

  // Required role assignment
  @IsNotEmpty()
  @IsNumber()
  roleId: number;

  // Optional employment-related fields
  @IsOptional()
  @IsString()
  position?: string;

  @IsOptional()
  @IsNumber()
  departmentId?: number;

  @IsOptional()
  @IsDateString()
  hireDate?: Date;
}
