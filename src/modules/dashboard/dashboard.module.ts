import { Module } from '@nestjs/common';
import { EmployeesModule } from '../employees/employees.module';
import { TimeAttendanceModule } from '../time-attendance/time-attendance.module';
import { PayrollsModule } from '../payrolls/payrolls.module';
import { LeaveRequestsModule } from '../leave-requests/leave-requests.module';
import { ProjectsModule } from '../projects/projects.module';
import { HrDashboardController } from './controllers/hr.dashboard.controller';
import { DashboardService } from './services/dashboard.service';
import { AdminDashboardController } from './controllers/admin.dashboard.controller';
import { EmployeeDashboardController } from './controllers/employee.dashboard.controller';

@Module({
  imports: [
    EmployeesModule,
    TimeAttendanceModule,
    PayrollsModule,
    LeaveRequestsModule,
    ProjectsModule,
  ],
  controllers: [
    AdminDashboardController,
    HrDashboardController,
    EmployeeDashboardController,
  ],
  providers: [DashboardService],
  exports: [DashboardService],
})
export class DashboardModule {}
