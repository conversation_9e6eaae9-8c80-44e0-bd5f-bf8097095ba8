import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Department } from '../../../models/department.model';
import { CreateDepartmentDto } from '../dto/create-department.dto';
import { UpdateDepartmentDto } from '../dto/update-department.dto';

@Injectable()
export class DepartmentsService {
  constructor(
    @InjectModel(Department)
    private departmentModel: typeof Department,
  ) {}

  async findAll(): Promise<Department[]> {
    return this.departmentModel.findAll();
  }

  async findOne(id: number): Promise<Department> {
    const department = await this.departmentModel.findByPk(id);

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    return department;
  }

  async create(createDepartmentDto: CreateDepartmentDto): Promise<Department> {
    return this.departmentModel.create(createDepartmentDto as any);
  }

  async update(
    id: number,
    updateDepartmentDto: UpdateDepartmentDto,
  ): Promise<Department> {
    const department = await this.findOne(id);
    await department.update(updateDepartmentDto);
    return department;
  }

  async remove(id: number): Promise<void> {
    const department = await this.findOne(id);
    await department.destroy();
  }
}
