import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { LeaveRequest } from '../../../models/leave-request.model';
import { CreateLeaveRequestDto } from '../dto/create-leave-request.dto';
import { UpdateLeaveRequestDto } from '../dto/update-leave-request.dto';

@Injectable()
export class LeaveRequestsService {
  constructor(
    @InjectModel(LeaveRequest)
    private leaveRequestModel: typeof LeaveRequest,
  ) {}

  async create(
    createLeaveRequestDto: CreateLeaveRequestDto,
  ): Promise<LeaveRequest> {
    return this.leaveRequestModel.create({ ...createLeaveRequestDto } as any);
  }

  async findAll(): Promise<LeaveRequest[]> {
    return this.leaveRequestModel.findAll({
      include: { all: true },
    });
  }

  async findByEmployeeId(employeeId: number): Promise<LeaveRequest[]> {
    return this.leaveRequestModel.findAll({
      where: { employeeId },
      include: { all: true },
    });
  }

  async findByStatus(status: string): Promise<LeaveRequest[]> {
    return this.leaveRequestModel.findAll({
      where: { status },
      include: { all: true },
    });
  }

  async findOne(id: number): Promise<LeaveRequest> {
    const leaveRequest = await this.leaveRequestModel.findByPk(id, {
      include: { all: true },
    });

    if (!leaveRequest) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }

    return leaveRequest;
  }

  async update(
    id: number,
    updateLeaveRequestDto: UpdateLeaveRequestDto,
  ): Promise<LeaveRequest> {
    const leaveRequest = await this.findOne(id);
    await leaveRequest.update(updateLeaveRequestDto as any);
    return this.findOne(id);
  }

  async approve(id: number, approverId: number): Promise<LeaveRequest> {
    const leaveRequest = await this.findOne(id);
    await leaveRequest.update({
      status: 'approved',
      approvedBy: approverId,
    });
    return this.findOne(id);
  }

  async reject(id: number, approverId: number): Promise<LeaveRequest> {
    const leaveRequest = await this.findOne(id);
    await leaveRequest.update({
      status: 'rejected',
      approvedBy: approverId,
    });
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const leaveRequest = await this.findOne(id);
    await leaveRequest.destroy();
  }
}
