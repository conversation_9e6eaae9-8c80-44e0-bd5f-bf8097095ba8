import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { LeaveRequest } from '../../models/leave-request.model';
import { LeaveRequestsController } from './controllers/leave-requests.controller';
import { LeaveRequestsService } from './services/leave-requests.service';

@Module({
  imports: [SequelizeModule.forFeature([LeaveRequest])],
  controllers: [LeaveRequestsController],
  providers: [LeaveRequestsService],
  exports: [LeaveRequestsService],
})
export class LeaveRequestsModule {}
