import {
  IsString,
  IsN<PERSON>ber,
  IsOptional,
  IsEnum,
  IsDate,
} from 'class-validator';
import { Type } from 'class-transformer';

enum LeaveRequestStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

enum LeaveType {
  ANNUAL = 'annual',
  SICK = 'sick',
  UNPAID = 'unpaid',
  MATERNITY = 'maternity',
  PATERNITY = 'paternity',
  BEREAVEMENT = 'bereavement',
  OTHER = 'other',
}

export class CreateLeaveRequestDto {
  @IsNumber()
  employeeId: number;

  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @Type(() => Date)
  @IsDate()
  endDate: Date;

  @IsEnum(LeaveType)
  type: string;

  @IsString()
  reason: string;

  @IsOptional()
  @IsEnum(LeaveRequestStatus)
  status?: string = LeaveRequestStatus.PENDING;

  @IsOptional()
  @IsNumber()
  approvedBy?: number;
}
