import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { LeaveRequestsService } from '../services/leave-requests.service';
import { LeaveRequest } from '../../../models/leave-request.model';
import { CreateLeaveRequestDto } from '../dto/create-leave-request.dto';
import { UpdateLeaveRequestDto } from '../dto/update-leave-request.dto';
// import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('leave-requests')
// @UseGuards(JwtAuthGuard)
export class LeaveRequestsController {
  constructor(private readonly leaveRequestsService: LeaveRequestsService) {}

  @Post()
  async create(
    @Body() createLeaveRequestDto: CreateLeaveRequestDto,
  ): Promise<LeaveRequest> {
    return this.leaveRequestsService.create(createLeaveRequestDto);
  }

  @Get()
  async findAll(@Query('status') status?: string): Promise<LeaveRequest[]> {
    if (status) {
      return this.leaveRequestsService.findByStatus(status);
    }
    return this.leaveRequestsService.findAll();
  }

  @Get('employee/:employeeId')
  async findByEmployeeId(
    @Param('employeeId') employeeId: number,
  ): Promise<LeaveRequest[]> {
    return this.leaveRequestsService.findByEmployeeId(employeeId);
  }

  @Get(':id')
  async findOne(@Param('id') id: number): Promise<LeaveRequest> {
    return this.leaveRequestsService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateLeaveRequestDto: UpdateLeaveRequestDto,
  ): Promise<LeaveRequest> {
    return this.leaveRequestsService.update(id, updateLeaveRequestDto);
  }

  @Patch(':id/approve')
  async approve(
    @Param('id') id: number,
    @Body('approverId') approverId: number,
  ): Promise<LeaveRequest> {
    return this.leaveRequestsService.approve(id, approverId);
  }

  @Patch(':id/reject')
  async reject(
    @Param('id') id: number,
    @Body('approverId') approverId: number,
  ): Promise<LeaveRequest> {
    return this.leaveRequestsService.reject(id, approverId);
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<void> {
    return this.leaveRequestsService.remove(id);
  }
}
