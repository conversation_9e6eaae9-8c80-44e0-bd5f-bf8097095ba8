import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  BadRequestException,
  Logger,
  Ip,
  Put,
  Delete,
} from '@nestjs/common';
import { AuthService } from '../services/auth.service';
import { LoginDto } from '../dto';
import { LocalAuthGuard } from '../guards/local-auth.guard';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

import { SignupDto } from '../dto/signup.dto';
import {
  AuthenticatedOnly,
  PublicRoute,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import {
  RefreshTokenDto,
  ChangePasswordDto,
  ForgotPasswordDto,
  ResetPasswordDto,
} from '../dto';
import { UserRequestI } from '../auth.interfaces';

@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @PublicRoute()
  @Post('signup')
  @HttpCode(HttpStatus.CREATED)
  async signup(@Body() signupDto: SignupDto, @Ip() ip: string) {
    this.logger.log(`Signup attempt from IP: ${ip}`);

    try {
      const data = await this.authService.userSignup(signupDto);

      this.logger.log(`User signup successful: ${signupDto.email}`);

      return {
        message: 'Account created successfully',
        data: {
          user: {
            id: data.id,
            email: data.email,
            firstName: data.firstName,
            lastName: data.lastName,
            role: data.role,
            redirectTo: data.redirectTo,
          },
          tokens: {
            accessToken: data.accessToken,
            refreshToken: data.refreshToken,
          },
        },
      };
    } catch (error) {
      this.logger.error(
        `Signup failed for ${signupDto.email}: ${error.message}`,
      );
      throw error;
    }
  }

  @PublicRoute()
  @UseGuards(LocalAuthGuard)
  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: LoginDto, @Ip() ip: string) {
    this.logger.log(
      `Login attempt from IP: ${ip} for email: ${loginDto.email}`,
    );

    try {
      const data = await this.authService.userLogin(loginDto);

      this.logger.log(`User login successful: ${loginDto.email}`);

      return {
        message: 'Login successful',
        data: {
          user: {
            id: data.id,
            email: data.email,
            firstName: data.firstName,
            lastName: data.lastName,
            profileImage: data.profileImage,
            role: data.role,
            scope: data.scope,
            redirectTo: data.redirectTo,
          },
          tokens: {
            accessToken: data.accessToken,
            refreshToken: data.refreshToken,
          },
        },
      };
    } catch (error) {
      this.logger.error(`Login failed for ${loginDto.email}: ${error.message}`);
      throw error;
    }
  }

  @PublicRoute()
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  async refreshTokens(
    @Body() refreshTokenDto: RefreshTokenDto,
    @Ip() ip: string,
  ) {
    this.logger.log(`Token refresh attempt from IP: ${ip}`);

    if (!refreshTokenDto.refreshToken) {
      throw new BadRequestException('Refresh token is required');
    }

    try {
      const data = await this.authService.refreshTokens(
        refreshTokenDto.refreshToken,
      );

      this.logger.log(`Token refresh successful for user: ${data.id}`);

      return {
        message: 'Tokens refreshed successfully',
        data: {
          user: {
            id: data.id,
            email: data.email,
            firstName: data.firstName,
            lastName: data.lastName,
            role: data.role,
            scope: data.scope,
          },
          tokens: {
            accessToken: data.accessToken,
            refreshToken: data.refreshToken,
          },
        },
      };
    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`);
      throw error;
    }
  }

  @AuthenticatedOnly()
  @UseGuards(JwtAuthGuard)
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  async logout(@Req() req: UserRequestI) {
    this.logger.log(`Logout request from user: ${req.user.id}`);

    try {
      const authHeader = req.headers['authorization'];
      if (authHeader) {
        const token = authHeader.split(' ')[1];
        await this.authService.invalidateToken(token);
      }

      this.logger.log(`User logout successful: ${req.user.id}`);

      return {
        message: 'Logout successful',
      };
    } catch (error) {
      this.logger.error(
        `Logout failed for user ${req.user.id}: ${error.message}`,
      );
      throw error;
    }
  }

  @AuthenticatedOnly()
  @UseGuards(JwtAuthGuard)
  @Get('profile')
  async getProfile(@Req() req: UserRequestI) {
    try {
      const profile = await this.authService.getUserProfile(req.user.id);

      return {
        message: 'Profile retrieved successfully',
        data: profile,
      };
    } catch (error) {
      this.logger.error(
        `Profile retrieval failed for user ${req.user.id}: ${error.message}`,
      );
      throw error;
    }
  }

  @AuthenticatedOnly()
  @UseGuards(JwtAuthGuard)
  @Put('change-password')
  @HttpCode(HttpStatus.OK)
  async changePassword(
    @Req() req: UserRequestI,
    @Body() changePasswordDto: ChangePasswordDto,
  ) {
    this.logger.log(`Password change request from user: ${req.user.id}`);

    try {
      await this.authService.changePassword(req.user.id, changePasswordDto);

      this.logger.log(`Password changed successfully for user: ${req.user.id}`);

      return {
        message: 'Password changed successfully',
      };
    } catch (error) {
      this.logger.error(
        `Password change failed for user ${req.user.id}: ${error.message}`,
      );
      throw error;
    }
  }

  @PublicRoute()
  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    this.logger.log(
      `Password reset request for email: ${forgotPasswordDto.email}`,
    );

    try {
      await this.authService.forgotPassword(forgotPasswordDto.email);

      return {
        message:
          'If an account with that email exists, a password reset link has been sent',
      };
    } catch (error) {
      this.logger.error(`Forgot password failed: ${error.message}`);
      // Don't throw error to prevent email enumeration
      return {
        message:
          'If an account with that email exists, a password reset link has been sent',
      };
    }
  }

  @PublicRoute()
  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    this.logger.log(`Password reset attempt with token`);

    try {
      await this.authService.resetPassword(resetPasswordDto);

      this.logger.log(`Password reset successful`);

      return {
        message: 'Password reset successfully',
      };
    } catch (error) {
      this.logger.error(`Password reset failed: ${error.message}`);
      throw error;
    }
  }

  @AuthenticatedOnly()
  @UseGuards(JwtAuthGuard)
  @Delete('revoke-all-sessions')
  @HttpCode(HttpStatus.OK)
  async revokeAllSessions(@Req() req: UserRequestI) {
    this.logger.log(`Revoke all sessions request from user: ${req.user.id}`);

    try {
      await this.authService.revokeAllUserSessions(req.user.id);

      this.logger.log(`All sessions revoked for user: ${req.user.id}`);

      return {
        message: 'All sessions have been revoked successfully',
      };
    } catch (error) {
      this.logger.error(
        `Session revocation failed for user ${req.user.id}: ${error.message}`,
      );
      throw error;
    }
  }
}
