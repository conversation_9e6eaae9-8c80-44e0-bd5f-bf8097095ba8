import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UnauthorizedException,
  Get,
  UseGuards,
  Req,
} from '@nestjs/common';

import { AdminLoginDto } from '../dto/admin-login.dto';
import { AuthService } from '../services/auth.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { AdminRequestI } from '../auth.interfaces';
import { PublicRoute } from '../../roles-and-permissions/permissions/decorators/permission.decorator';

@Controller('admin')
export class AdminAuthController {
  constructor(private readonly authService: AuthService) {}

  @PublicRoute()
  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: AdminLoginDto) {
    const data = await this.authService.adminLogin(loginDto);

    return {
      message: 'Login successful',
      data: data,
    };
  }

  @PublicRoute()
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  async refresh(@Body('refreshToken') refreshToken: string) {
    return this.authService.refreshTokens(refreshToken);
  }

  @UseGuards(JwtAuthGuard)
  @Get('current')
  async getCurrentAdmin(@Req() req: AdminRequestI) {
    const currentUserData = await this.authService.getCurrentAdmin(req.user.id);

    if (!currentUserData) {
      throw new UnauthorizedException('Admin not found');
    }
    const { password: _, ...userData } = currentUserData;

    return {
      message: 'Current admin retrieved successfully',
      data: userData,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  async logout(@Req() req: AdminRequestI) {
    const token = req.headers?.['authorization']?.split(' ')[1];

    await this.authService.invalidateToken(token);

    return {
      message: 'Logout successful',
    };
  }
}
