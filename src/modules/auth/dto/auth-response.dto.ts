export interface AuthResponseDto {
  message: string;
  data: {
    user: {
      id: number;
      email: string;
      firstName: string;
      lastName: string;
      profileImage?: string;
      role: string;
      scope?: string;
      redirectTo?: string;
    };
    tokens: {
      accessToken: string;
      refreshToken: string;
    };
  };
}

export interface ProfileResponseDto {
  message: string;
  data: {
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    username: string;
    profileImage?: string;
    status: string;
    redirectTo?: string;
  };
}

export interface MessageResponseDto {
  message: string;
}
