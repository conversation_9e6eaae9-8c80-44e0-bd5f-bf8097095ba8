import { SetMetadata } from '@nestjs/common';

export const RATE_LIMIT_KEY = 'rateLimit';

export interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  max: number; // Maximum number of requests per window
}

export const RateLimit = (options: RateLimitOptions) =>
  SetMetadata(RATE_LIMIT_KEY, options);

// Predefined rate limits for common auth operations
export const LoginRateLimit = () => RateLimit({ windowMs: 15 * 60 * 1000, max: 5 }); // 5 attempts per 15 minutes
export const SignupRateLimit = () => RateLimit({ windowMs: 60 * 60 * 1000, max: 3 }); // 3 attempts per hour
export const ForgotPasswordRateLimit = () => RateLimit({ windowMs: 60 * 60 * 1000, max: 3 }); // 3 attempts per hour
