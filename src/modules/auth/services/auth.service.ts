import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../../users/services/users.service';
import { User } from '../../../models/users-models/user.model';
import { AdminLoginDto } from '../dto/admin-login.dto';
import { TokenPayloadI, UserAuthPayload } from '../auth.interfaces';
import * as bcrypt from 'bcrypt';
import { AppConfig } from 'src/config/config.interface';
import { ConfigService } from '@nestjs/config';
import { UserRoleService } from 'src/modules/roles-and-permissions/user-role/user-role.service';
import { LoginDto } from '../dto';
import { SignupDto } from '../dto/signup.dto';
import { RolesService } from 'src/modules/roles-and-permissions/roles/roles.service';
import { RedirectSection } from 'src/utils/redirect-section.enum';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly userRoleService: UserRoleService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService<AppConfig>,
    private readonly rolesService: RolesService,
  ) {}

  async adminLogin(loginDto: AdminLoginDto): Promise<UserAuthPayload> {
    const { email, password } = loginDto;

    const user = await this.usersService.findByEmail(email);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await this.usersService.validatePassword(
      password,
      user.password,
    );

    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const userRole = await this.userRoleService.getRoleForUser(user.id);

    const tokens = await this.generateTokens({
      sub: user.id,
      email: user.email,
      role: userRole.role.name,
      scope: userRole.role.scope,
    });

    await this.usersService.storeUserRefreshToken(user.id, tokens.refreshToken);

    const userData: UserAuthPayload = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      profileImage: user.profileImage,
      role: userRole.role.name,
      scope: userRole.role.scope,
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
    };

    return userData;
  }

  async invalidateToken(token: string): Promise<void> {
    const decoded = this.jwtService.decode(token) as TokenPayloadI;

    if (!decoded?.sub) return;

    const user = await this.usersService.findOne(decoded.sub);
    if (!user || !user.refreshToken) return;

    const isMatch = await bcrypt.compare(token, user.refreshToken);
    if (!isMatch) return;

    await this.usersService.updateUser(user.id, { refreshToken: null });
  }

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.usersService.findByEmail(email);

    if (!user) return null;

    const isValid = await this.usersService.validatePassword(
      password,
      user.password,
    );

    if (!isValid) return null;

    const { password: _, ...result } = user;
    return result;
  }

  async userSignup(signupDto: SignupDto): Promise<UserAuthPayload> {
    const { username, email, password, firstName, lastName } = signupDto;

    const companySuperAdminRole = await this.rolesService.findRoleByName(
      'Company Super Admin',
    );

    const user = await this.usersService.createUser({
      username,
      email,
      password,
      firstName,
      lastName,
      redirectTo: RedirectSection.CREATE_COMPANY,
      roleId: companySuperAdminRole.id,
    });

    const tokens = await this.generateTokens({
      sub: user.id,
      email: user.email,
      role: companySuperAdminRole.name,
      scope: companySuperAdminRole.scope,
    });

    await this.usersService.storeUserRefreshToken(user.id, tokens.refreshToken);

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      profileImage: user.profileImage,
      role: companySuperAdminRole.name,
      scope: companySuperAdminRole.scope,
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      redirectTo: user.redirectTo,
    };
  }

  async userLogin(loginDto: LoginDto): Promise<UserAuthPayload> {
    const { email, password } = loginDto;

    const user = await this.usersService.findByEmail(email);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await this.usersService.validatePassword(
      password,
      user.password,
    );

    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const userRole = await this.userRoleService.getRoleForUser(user.id);

    const payload: TokenPayloadI = {
      sub: user.id,
      email: user.email,
      role: userRole.role.name,
      scope: userRole.role.scope,
    };

    const tokens = await this.generateTokens(payload);

    await this.usersService.storeUserRefreshToken(user.id, tokens.refreshToken);

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      profileImage: user.profileImage,
      role: userRole.role.name,
      scope: userRole.role.scope,
      accessToken: tokens.accessToken,
      refreshToken: user.refreshToken,
      redirectTo: user.redirectTo,
    };
  }

  async getUserProfile(userId: number): Promise<Partial<User>> {
    const user = await this.usersService.findOne(userId);
    console.log('her eis data', user);
    return user;
  }

  async generateTokens(payload: TokenPayloadI) {
    const jwtConfig = this.configService.get<AppConfig['jwt']>('jwt');

    const accessToken = this.jwtService.sign(payload, {
      secret: jwtConfig.secret,
      expiresIn: jwtConfig.expiresIn,
    });

    const refreshToken = this.jwtService.sign(payload, {
      secret: jwtConfig.refreshSecret,
      expiresIn: jwtConfig.refreshExpiresIn,
    });

    return { accessToken, refreshToken };
  }

  async refreshTokens(refreshToken: string): Promise<UserAuthPayload> {
    const jwtConfig = this.configService.get<AppConfig['jwt']>('jwt');

    const decoded = this.jwtService.verify(refreshToken, {
      secret: jwtConfig.refreshSecret,
    });

    const user = await this.usersService.findOne(decoded.sub);

    if (!user || !(await bcrypt.compare(refreshToken, user.refreshToken))) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const userRole = await this.userRoleService.getRoleForUser(user.id);

    const role = userRole.role.name;

    const payload: TokenPayloadI = {
      sub: user.id,
      email: user.email,
      role: role,
      scope: userRole.role.scope,
    };

    const tokens = await this.generateTokens(payload);

    await this.usersService.updateUser(user.id, {
      refreshToken: tokens.refreshToken,
    });

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      profileImage: user.profileImage,
      role: role,
      scope: userRole.role.scope,
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
    };
  }

  async getCurrentAdmin(adminId: number): Promise<Partial<User>> {
    const admin = await this.usersService.findOne(adminId);
    if (!admin) {
      throw new UnauthorizedException('Admin not found');
    }
    return admin;
  }

  async changePassword(userId: number, changePasswordDto: any): Promise<void> {
    const { currentPassword, newPassword, confirmPassword } = changePasswordDto;

    if (newPassword !== confirmPassword) {
      throw new BadRequestException(
        'New password and confirm password do not match',
      );
    }

    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const isCurrentPasswordValid = await this.usersService.validatePassword(
      currentPassword,
      user.password,
    );

    if (!isCurrentPasswordValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    await this.usersService.updateUser(userId, { password: newPassword });
  }

  async forgotPassword(email: string): Promise<void> {
    const user = await this.usersService.findByEmail(email);
    if (!user) {
      // Don't reveal if email exists or not for security
      return;
    }

    // Generate reset token (you might want to store this in database)
    const resetToken = this.jwtService.sign(
      { sub: user.id, type: 'password-reset' },
      { expiresIn: '1h' },
    );

    // TODO: Send email with reset link
    // await this.emailService.sendPasswordResetEmail(user.email, resetToken);
  }

  async resetPassword(resetPasswordDto: any): Promise<void> {
    const { token, newPassword, confirmPassword } = resetPasswordDto;

    if (newPassword !== confirmPassword) {
      throw new BadRequestException(
        'New password and confirm password do not match',
      );
    }

    try {
      const decoded = this.jwtService.verify(token);

      if (decoded.type !== 'password-reset') {
        throw new UnauthorizedException('Invalid reset token');
      }

      const user = await this.usersService.findOne(decoded.sub);
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      await this.usersService.updateUser(user.id, { password: newPassword });

      // Invalidate all existing sessions
      await this.revokeAllUserSessions(user.id);
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired reset token');
    }
  }

  async revokeAllUserSessions(userId: number): Promise<void> {
    await this.usersService.updateUser(userId, { refreshToken: null });
  }
}
