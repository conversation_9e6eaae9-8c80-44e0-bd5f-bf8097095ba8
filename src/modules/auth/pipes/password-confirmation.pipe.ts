import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';

@Injectable()
export class PasswordConfirmationPipe implements PipeTransform {
  transform(value: any) {
    if (value.newPassword && value.confirmPassword) {
      if (value.newPassword !== value.confirmPassword) {
        throw new BadRequestException('Password and confirm password do not match');
      }
    }
    
    if (value.password && value.confirmPassword) {
      if (value.password !== value.confirmPassword) {
        throw new BadRequestException('Password and confirm password do not match');
      }
    }
    
    return value;
  }
}
