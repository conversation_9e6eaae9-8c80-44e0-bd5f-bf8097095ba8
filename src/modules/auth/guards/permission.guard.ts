import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { PermissionService } from '../../roles-and-permissions/permissions/permission.service';
import {
  PERMISSION_KEY,
  PUBLIC_ROUTE_KEY,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { IPermissionDecoratorOptions } from '../auth.interfaces';
import { ROLE_SCOPE_ENUM } from '../../../utils/enums';
import { ROLES_ENUM } from 'src/modules/roles-and-permissions/roles/utils/enums';

@Injectable()
export class PermissionGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(PermissionGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly permissionService: PermissionService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();

    // Check for public routes - skip all authentication and permission checks
    const isPublicRoute = this.reflector.getAllAndOverride<boolean>(
      PUBLIC_ROUTE_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (isPublicRoute) {
      this.logger.debug(
        `Public route access granted for ${context.getClass().name}.${context.getHandler().name}`,
      );
      return true;
    }

    // First, authenticate the user
    const isAuthenticated = await super.canActivate(context);
    if (!isAuthenticated) {
      throw new UnauthorizedException('Authentication required');
    }

    const user = request.user as any;

    if (!user?.id) {
      throw new UnauthorizedException('User not found in request');
    }

    // Get permission requirements from decorator
    const permissionOptions =
      this.reflector.getAllAndOverride<IPermissionDecoratorOptions>(
        PERMISSION_KEY,
        [context.getHandler(), context.getClass()],
      );

    // If no permission requirements specified, allow access for authenticated users
    if (!permissionOptions) {
      this.logger.debug(
        `No permission requirements for ${context.getClass().name}.${context.getHandler().name}`,
      );
      return true;
    }

    return this.checkUserPermission(user.id, permissionOptions);
  }

  private async checkUserPermission(
    userId: number,
    permissionOptions: IPermissionDecoratorOptions,
  ): Promise<boolean> {
    const { section, action, scope } = permissionOptions;

    try {
      // Check if user is platform super admin
      const isPlatformSuperAdmin = await this.permissionService.isUserInRole(
        userId,
        ROLES_ENUM.PLATFORM_SUPER_ADMIN,
        ROLE_SCOPE_ENUM.PLATFORM,
      );

      if (isPlatformSuperAdmin) {
        this.logger.debug(
          `Platform user ${userId} granted access to ${section}:${action}`,
        );
        return true;
      }

      // For platform-scoped operations, only platform users can access
      if (scope === ROLE_SCOPE_ENUM.PLATFORM) {
        const hasPermission = await this.permissionService.checkPermission(
          userId,
          section,
          action,
        );

        if (hasPermission) {
          this.logger.debug(
            `Platform user ${userId} granted access to ${section}:${action}`,
          );
          return true;
        }

        throw new ForbiddenException(
          `Insufficient permissions for ${section}:${action}`,
        );
      }

      // For company-scoped operations, check company permissions
      if (scope === ROLE_SCOPE_ENUM.COMPANY) {
        // if user is company super admin, then allow access
        const isCompanySuperAdmin = await this.permissionService.isUserInRole(
          userId,
          ROLES_ENUM.COMPANY_SUPER_ADMIN,
          ROLE_SCOPE_ENUM.COMPANY,
        );

        if (isCompanySuperAdmin) {
          return true;
        }

        const companyId = await this.permissionService.getUserCompanyId(userId);
        if (!companyId) {
          throw new ForbiddenException('User does not belong to any company');
        }

        const hasPermission = await this.permissionService.checkPermission(
          userId,
          section,
          action,
          companyId,
        );

        if (hasPermission) {
          this.logger.debug(
            `Company user ${userId} granted access to ${section}:${action}`,
          );
          return true;
        }
      }

      // Permission denied
      this.logger.warn(
        `Permission denied for user ${userId} on ${section}:${action} (scope: ${scope})`,
      );
      throw new ForbiddenException(
        `Insufficient permissions for ${section}:${action}`,
      );
    } catch (error) {
      if (
        error instanceof ForbiddenException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      this.logger.error(
        `Error checking permissions for user ${userId}:`,
        error,
      );
      throw new ForbiddenException('Permission check failed');
    }
  }
}
