import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { CreateTimeAttendanceDto } from '../dto/create-time-attendance.dto';
import { UpdateTimeAttendanceDto } from '../dto/update-time-attendance.dto';
import {
  UserRequestI,
  RequestUserObjectI,
} from 'src/modules/auth/auth.interfaces';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { User } from 'src/models/users-models/user.model';
import { Op } from 'sequelize';
import {
  getDateRange,
  getDaysInMonth,
  isLateClockIn,
} from 'src/utils/date.util';
import {
  calculateLateAndAbsentDays,
  calculateOvertime,
  calculateWorkedHours,
} from '../time.attendence.utils';
import { ATTENDENCE_STATUS } from '../interface/time.attendence.interface';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';

@Injectable()
export class EmployeeTimeAttendanceService {
  constructor(
    @InjectModel(TimeAttendance)
    private readonly timeAttendanceModel: typeof TimeAttendance,
    private readonly crudService: CrudHelperService,
  ) {}

  async clockIn(user: RequestUserObjectI): Promise<Partial<TimeAttendance>> {
    try {
      const now = new Date();

      const lastAttendance = await this.crudService.findOne<TimeAttendance>(
        TimeAttendance,
        {
          where: { userId: user.id },
          order: [['createdAt', 'DESC']],
        },
      );

      if (lastAttendance && !lastAttendance.clockOutTime) {
        throw new BadRequestException(
          'You have already checked in. Please check out before clocking in again.',
        );
      }

      const attendanceData = {
        userId: user.id,
        clockInTime: now,
        date: now,
      };

      const checkedIn = await this.crudService.create<TimeAttendance>(
        TimeAttendance,
        attendanceData,
      );

      console.log('✅ Stored attendance:', checkedIn);
      return checkedIn;
    } catch (error) {
      console.error('❌ Error during clock-in:', error);
      throw error;
    }
  }

  async clockOut(user: RequestUserObjectI): Promise<TimeAttendance> {
    try {
      const lastClockIn = await this.timeAttendanceModel.findOne({
        where: {
          userId: user.id,
          clockOutTime: null,
        },
        order: [['clockInTime', 'DESC']],
      });

      if (!lastClockIn) {
        throw new NotFoundException(
          'No active clock-in record found to clock out.',
        );
      }

      lastClockIn.clockOutTime = new Date();
      await lastClockIn.save();
      return lastClockIn;
    } catch (error) {
      console.error('❌ Error storing attendance:', error);
      throw error;
    }
  }

  async findPaginated(
    user: RequestUserObjectI,
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<TimeAttendance>>> {
    const isPlatformUser = user.scope === ROLE_SCOPE_ENUM.PLATFORM;

    const whereClause = isPlatformUser ? {} : { userId: user.id };

    return this.crudService.paginateWithQuery<TimeAttendance>(
      this.timeAttendanceModel,
      {
        page,
        limit,
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: User,
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
        where: whereClause,
      },
    );
  }

  async findByDate(date: string): Promise<TimeAttendance[]> {
    return this.timeAttendanceModel.findAll({
      where: { date },
      include: [
        { model: User, attributes: ['id', 'firstName', 'lastName', 'email'] },
      ],
      order: [['clockInTime', 'ASC']],
    });
  }

  async generateMonthlyReport({
    month,
    year,
  }: {
    month: number;
    year: number;
  }) {
    const { startDate, endDate } = getDateRange(month, year);

    const attendances = await this.timeAttendanceModel.findAll({
      where: {
        date: {
          [Op.between]: [startDate, endDate],
        },
      },
    });

    const daysInMonth = getDaysInMonth(month, year);
    const today = new Date();

    const lateAndAbsentStats = calculateLateAndAbsentDays(
      attendances,
      daysInMonth,
      month,
      year,
      today,
    );

    const workedStats = calculateWorkedHours(attendances, daysInMonth);
    const overtimeStats = calculateOvertime(attendances);

    return {
      monthStats: lateAndAbsentStats,
      workedHours: workedStats,
      overTime: overtimeStats,
    };
  }

  findAll(): Promise<TimeAttendance[]> {
    return this.crudService.findAll<TimeAttendance>(TimeAttendance, {
      include: { all: true },
      order: [['createdAt', 'DESC']],
    });
  }

  findByEmployeeId(userId: number): Promise<TimeAttendance[]> {
    return this.crudService.findAll<TimeAttendance>(TimeAttendance, {
      where: { userId },
      include: { all: true },
      order: [['createdAt', 'DESC']],
    });
  }

  findOne(id: number): Promise<Partial<TimeAttendance>> {
    return this.crudService.findOne<TimeAttendance>(TimeAttendance, {
      where: { id },
      include: { all: true },
    });
  }

  async update(
    id: number,
    dto: UpdateTimeAttendanceDto,
  ): Promise<Partial<TimeAttendance>> {
    await this.findOne(id); // ensure existence
    await this.crudService.update(TimeAttendance, dto, { where: { id } });
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.findOne(id);
    await this.crudService.delete(TimeAttendance, { where: { id } });
  }
}
