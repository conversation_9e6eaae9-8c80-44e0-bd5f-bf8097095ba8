import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { UpdateTimeAttendanceDto } from '../dto/update-time-attendance.dto';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { EmployeeTimeAttendanceService } from '../services/time-attendance.service';
import { CreateTimeAttendanceDto } from '../dto/create-time-attendance.dto';

@Controller('time-attendance')
@UseGuards(JwtAuthGuard)
export class TimeAttendanceController {
  constructor(
    private readonly timeAttendanceService: EmployeeTimeAttendanceService,
  ) {}

  @Post('clock-in')
  async clockIn(@Req() req: UserRequestI): Promise<Partial<TimeAttendance>> {
    const user = req.user;
    return this.timeAttendanceService.clockIn(user);
  }

  @Patch('clock-out')
  async clockOut(@Req() req: UserRequestI): Promise<TimeAttendance> {
    const user = req.user;
    return this.timeAttendanceService.clockOut(user);
  }

  @Get('paginated')
  async findPaginated(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Req() req: UserRequestI,
  ): Promise<PaginatedResult<Partial<TimeAttendance>>> {
    const user = req.user;
    return this.timeAttendanceService.findPaginated(
      user,
      Number(page),
      Number(limit),
    );
  }

  @Get('by-date')
  async findByDate(@Query('date') date: string): Promise<TimeAttendance[]> {
    if (!date)
      throw new BadRequestException('Date query parameter is required');
    return this.timeAttendanceService.findByDate(date);
  }

  @Get('report')
  async getMonthlyReport(
    @Query('month') month: number,
    @Query('year') year: number,
  ) {
    return this.timeAttendanceService.generateMonthlyReport({ month, year });
  }

  @Get()
  async findAll(): Promise<TimeAttendance[]> {
    return this.timeAttendanceService.findAll();
  }

  @Get('employee/:employeeId')
  async findByEmployeeId(
    @Param('employeeId') employeeId: number,
  ): Promise<TimeAttendance[]> {
    return this.timeAttendanceService.findByEmployeeId(employeeId);
  }

  @Get(':id')
  async findOne(@Param('id') id: number): Promise<Partial<TimeAttendance>> {
    return this.timeAttendanceService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateTimeAttendanceDto: UpdateTimeAttendanceDto,
  ): Promise<Partial<TimeAttendance>> {
    return this.timeAttendanceService.update(id, updateTimeAttendanceDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<void> {
    return this.timeAttendanceService.remove(id);
  }
}
