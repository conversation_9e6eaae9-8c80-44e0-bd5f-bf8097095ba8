import { isLateClockIn } from 'src/utils/date.util';
import { TimeAttendance } from '../../models/time-attendance.model';

export function calculateLateAndAbsentDays(
  attendances: TimeAttendance[],
  daysInMonth: number,
  month: number,
  year: number,
  today: Date,
) {
  const clockInMap = new Map<string, Date>();

  // Ensure only one clock-in per day is evaluated
  for (const att of attendances) {
    const dateStr = new Date(att.date).toDateString();
    const existing = clockInMap.get(dateStr);
    if (!existing || att.clockInTime < existing) {
      clockInMap.set(dateStr, att.clockInTime);
    }
  }

  let lateDays = 0;
  for (const [_, clockInTime] of clockInMap.entries()) {
    if (isLateClockIn(clockInTime)) {
      lateDays++;
    }
  }

  let absentDays = 0;
  for (let i = 1; i <= daysInMonth; i++) {
    const date = new Date(year, month - 1, i);

    // Stop if it's today (for current month)
    if (
      date.getFullYear() === today.getFullYear() &&
      date.getMonth() === today.getMonth() &&
      date.getDate() === today.getDate()
    )
      break;

    // Skip weekends
    const dayOfWeek = date.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) continue;

    if (!clockInMap.has(date.toDateString())) {
      absentDays++;
    }
  }

  return {
    workedDays: clockInMap.size,
    LateDays: lateDays,
    absentDays,
  };
}

export function calculateWorkedHours(
  attendances: TimeAttendance[],
  days: number,
) {
  const plannedHours = days * 9;
  let workedHours = 0;

  for (const att of attendances) {
    if (att.clockInTime && att.clockOutTime) {
      const hours =
        (new Date(att.clockOutTime).getTime() -
          new Date(att.clockInTime).getTime()) /
        (1000 * 60 * 60);
      workedHours += hours;
    }
  }

  return {
    plannedHours,
    workedHours: Number(workedHours.toFixed(2)),
    difference: Number((workedHours - plannedHours).toFixed(2)),
  };
}

export function calculateOvertime(attendances: TimeAttendance[]) {
  let overtimeHours = 0;

  for (const att of attendances) {
    if (att.clockInTime && att.clockOutTime) {
      const hours =
        (new Date(att.clockOutTime).getTime() -
          new Date(att.clockInTime).getTime()) /
        (1000 * 60 * 60);
      if (hours > 9) overtimeHours += hours - 9;
    }
  }

  return { totalOvertimeHours: Number(overtimeHours.toFixed(2)) };
}
