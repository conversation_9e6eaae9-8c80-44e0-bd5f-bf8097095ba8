import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { PermissionService } from '../permission.service';
import { Permission } from 'src/models/roles-and-permissions/permission.model';
import { RequirePermission } from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/enums';

@Controller('admin/permissions')
export class AdminPermissionsController {
  constructor(private readonly permissionService: PermissionService) {}

  @Post()
  // @RequirePermission(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  create(@Body() dto: Partial<Permission>) {
    return this.permissionService.create(dto);
  }

  @Get()
  // @RequirePermission(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  findAll() {
    return this.permissionService.findAll();
  }

  @Get(':id')
  // @RequirePermission(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  findOne(@Param('id') id: string) {
    return this.permissionService.findById(+id);
  }

  @Patch(':id')
  // @RequirePermission(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  update(@Param('id') id: string, @Body() dto: Partial<Permission>) {
    return this.permissionService.update(+id, dto);
  }

  @Delete(':id')
  @RequirePermission(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  remove(@Param('id') id: string) {
    return this.permissionService.delete(+id);
  }
}
