import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UserRole } from '../../../models/roles-and-permissions/user-role.model';
import { Role } from '../../../models/roles-and-permissions/role.model';
import { Permission } from '../../../models/roles-and-permissions/permission.model';
import { PlatformSection } from '../../../models/roles-and-permissions/platform-section.model';
import { Company } from '../../../models/company.model';
import { EmploymentDetails } from '../../../models/users-models/employment-details.model';
import { CrudHelperService } from '../../../common/crud-helper/crud-helper.service';
import {
  IPermissionCheck,
  IUserPermissions,
  IPermission,
} from '../../auth/auth.interfaces';
import { ROLE_SCOPE_ENUM, PERMISSION_ACTION_ENUM } from '../../../utils/enums';
import { ROLES_ENUM } from '../roles/utils/enums';
import { RolesService } from '../roles/roles.service';

interface CachedUserData {
  userId: number;
  roleId: number;
  roleName: string;
  scope: string;
  companyId?: number;
  permissions: Map<string, IPermission>;
  lastUpdated: Date;
}

interface CachedPlatformSection {
  id: number;
  name: string;
  displayName: string;
  description?: string;
  isActive: boolean;
}

@Injectable()
export class PermissionService {
  private readonly logger = new Logger(PermissionService.name);
  private readonly userCache = new Map<number, CachedUserData>();
  private readonly platformSectionsCache = new Map<
    string,
    CachedPlatformSection
  >();
  private readonly sectionNameMappingCache = new Map<string, string>();
  // TODO: Move it to the config file and env
  private readonly CACHE_TTL = 10 * 60 * 1000; // 10 minutes
  private readonly SECTIONS_CACHE_TTL = 30 * 60 * 1000; // 30 minutes

  private platformSectionsLastFetched: Date | null = null;

  // Super Admin role cache
  private platformSuperAdminRoleCache: Partial<Role> | null = null;
  private platformSuperAdminRoleCacheTime: Date | null = null;
  // TODO: Move it to the config file and env
  private readonly PLATFORM_SUPER_ADMIN_CACHE_TTL = 30 * 60 * 1000; // 30 minutes
  // Company Super Admin role cache
  private companySuperAdminRoleCache: Partial<Role> | null = null;
  private companySuperAdminRoleCacheTime: Date | null = null;
  // TODO: Move it to the config file and env
  private readonly COMPANY_SUPER_ADMIN_CACHE_TTL = 30 * 60 * 1000; // 30 minutes

  constructor(
    @InjectModel(UserRole)
    private readonly userRoleModel: typeof UserRole,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(Permission)
    private readonly permissionModel: typeof Permission,
    @InjectModel(PlatformSection)
    private readonly platformSectionModel: typeof PlatformSection,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(EmploymentDetails)
    private readonly employmentDetailsModel: typeof EmploymentDetails,
    private readonly crudService: CrudHelperService,
    private readonly rolesService: RolesService,
  ) {}

  /**
   * Get the Platform Super Admin role (cached for performance)
   */
  private async getPlatformSuperAdminRole(): Promise<Partial<Role> | null> {
    // Check if cache is valid
    if (
      this.platformSuperAdminRoleCache &&
      this.platformSuperAdminRoleCacheTime &&
      Date.now() - this.platformSuperAdminRoleCacheTime.getTime() <
        this.PLATFORM_SUPER_ADMIN_CACHE_TTL
    ) {
      return this.platformSuperAdminRoleCache;
    }

    // Fetch Platform Super Admin role from database
    const platformSuperAdminRole =
      await this.rolesService.getRoleByNameAndScope(
        ROLES_ENUM.PLATFORM_SUPER_ADMIN,
        ROLE_SCOPE_ENUM.PLATFORM,
      );

    // Cache the result
    this.platformSuperAdminRoleCache = platformSuperAdminRole;
    this.platformSuperAdminRoleCacheTime = new Date();

    // Auto cleanup cache
    setTimeout(() => {
      this.platformSuperAdminRoleCache = null;
      this.platformSuperAdminRoleCacheTime = null;
    }, this.PLATFORM_SUPER_ADMIN_CACHE_TTL);

    return platformSuperAdminRole;
  }

  private async getCompanySuperAdminRole(): Promise<Partial<Role> | null> {
    if (
      this.companySuperAdminRoleCache &&
      this.companySuperAdminRoleCacheTime &&
      Date.now() - this.companySuperAdminRoleCacheTime.getTime() <
        this.COMPANY_SUPER_ADMIN_CACHE_TTL
    ) {
      return this.companySuperAdminRoleCache;
    }

    const companySuperAdminRole = await this.rolesService.getRoleByNameAndScope(
      ROLES_ENUM.COMPANY_SUPER_ADMIN,
      ROLE_SCOPE_ENUM.COMPANY,
    );

    this.companySuperAdminRoleCache = companySuperAdminRole;
    this.companySuperAdminRoleCacheTime = new Date();

    return companySuperAdminRole;
  }

  /**
   * Generic check if user has a specific role by role name and scope.
   * @param userId - User ID to check
   * @param roleName - Name of the role (e.g., 'PLATFORM_SUPER_ADMIN')
   * @param roleScope - Scope of the role (e.g., 'platform' or 'company')
   * @returns boolean indicating if user has the specified role
   */
  async isUserInRole(
    userId: number,
    roleName: ROLES_ENUM,
    roleScope: ROLE_SCOPE_ENUM,
  ): Promise<boolean> {
    let role: Partial<Role> | null = null;

    if (roleScope === ROLE_SCOPE_ENUM.PLATFORM) {
      role = await this.getPlatformSuperAdminRole();
    } else if (roleScope === ROLE_SCOPE_ENUM.COMPANY) {
      role = await this.getCompanySuperAdminRole();
    } else {
      // For future extensibility, fetch by name and scope
      role = await this.rolesService.getRoleByNameAndScope(roleName, roleScope);
    }

    if (!role?.id) {
      return false;
    }

    const userData = await this.getCachedUserData(userId);
    if (!userData) {
      return false;
    }

    return userData.roleId === role.id;
  }

  /**
   * Check if user is Platform Super Admin by comparing role IDs
   */
  async isUserPlatformSuperAdmin(userId: number): Promise<boolean> {
    return this.isUserInRole(
      userId,
      ROLES_ENUM.PLATFORM_SUPER_ADMIN,
      ROLE_SCOPE_ENUM.PLATFORM,
    );
  }

  /**
   * Check if user is Company Super Admin by comparing role IDs
   */
  async isUserCompanySuperAdmin(userId: number): Promise<boolean> {
    return this.isUserInRole(
      userId,
      ROLES_ENUM.COMPANY_SUPER_ADMIN,
      ROLE_SCOPE_ENUM.COMPANY,
    );
  }

  // ===== CRUD Operations =====

  /**
   * Create a new permission
   */
  create(data: Partial<Permission>) {
    return this.crudService.create(this.permissionModel, data);
  }

  /**
   * Get all permissions
   */
  findAll() {
    return this.crudService.findAll(this.permissionModel);
  }

  /**
   * Find permission by ID
   */
  findById(id: number) {
    return this.crudService.findOne(this.permissionModel, { where: { id } });
  }

  /**
   * Update permission
   */
  update(id: number, data: Partial<Permission>) {
    return this.crudService.update(this.permissionModel, data, {
      where: { id },
    });
  }

  /**
   * Delete permission
   */
  delete(id: number) {
    return this.crudService.delete(this.permissionModel, { where: { id } });
  }

  // ===== Permission Checking & Caching =====

  /**
   * Get cached user data or fetch from database
   */
  async getCachedUserData(userId: number): Promise<CachedUserData | null> {
    const cached = this.userCache.get(userId);

    if (cached && this.isCacheValid(cached.lastUpdated)) {
      return cached;
    }

    const userData = await this.fetchUserData(userId);

    if (userData) {
      this.userCache.set(userId, userData);
      // Auto cleanup cache
      setTimeout(() => this.userCache.delete(userId), this.CACHE_TTL);
    }

    return userData;
  }

  /**
   * Get cached platform sections or fetch from database
   */
  async getCachedPlatformSections(): Promise<
    Map<string, CachedPlatformSection>
  > {
    if (
      this.platformSectionsCache.size > 0 &&
      this.platformSectionsLastFetched &&
      this.isSectionsCacheValid(this.platformSectionsLastFetched)
    ) {
      return this.platformSectionsCache;
    }

    await this.fetchPlatformSections();
    return this.platformSectionsCache;
  }

  /**
   * Find platform section by controller name with intelligent matching
   */
  async findSectionByControllerName(
    controllerName: string,
  ): Promise<string | null> {
    // Check cache first
    const cacheKey = controllerName.toLowerCase();
    if (this.sectionNameMappingCache.has(cacheKey)) {
      return this.sectionNameMappingCache.get(cacheKey);
    }

    const sections = await this.getCachedPlatformSections();

    // Normalize controller name for matching
    const normalizedController = controllerName
      .toLowerCase()
      .replace(/controller$/, '')
      .replace(/s$/, ''); // Remove trailing 's' for plurals

    let bestMatch: string | null = null;
    let bestScore = 0;

    sections.forEach((section, sectionName) => {
      if (!section.isActive) return;

      const normalizedSection = sectionName.toLowerCase().replace(/-/g, '');
      const normalizedSectionDisplay = section.displayName
        .toLowerCase()
        .replace(/[^a-z]/g, '');

      // Exact match
      if (
        normalizedSection === normalizedController ||
        normalizedSectionDisplay === normalizedController
      ) {
        bestMatch = sectionName;
        bestScore = 100;
        return;
      }

      // Partial match scoring
      let score = 0;
      if (
        normalizedSection.includes(normalizedController) ||
        normalizedController.includes(normalizedSection)
      ) {
        score = 80;
      } else if (
        normalizedSectionDisplay.includes(normalizedController) ||
        normalizedController.includes(normalizedSectionDisplay)
      ) {
        score = 70;
      }

      // Fuzzy matching for common variations
      const variations = [
        normalizedController + 's',
        normalizedController.replace(/s$/, ''),
        normalizedController.replace(/ie$/, 'y'),
        normalizedController.replace(/y$/, 'ie'),
      ];

      variations.forEach((variant) => {
        if (
          normalizedSection === variant ||
          normalizedSectionDisplay === variant
        ) {
          score = Math.max(score, 90);
        }
      });

      if (score > bestScore) {
        bestScore = score;
        bestMatch = sectionName;
      }
    });

    // Cache the result (even if null)
    this.sectionNameMappingCache.set(cacheKey, bestMatch);

    // Auto cleanup mapping cache
    setTimeout(
      () => this.sectionNameMappingCache.delete(cacheKey),
      this.SECTIONS_CACHE_TTL,
    );

    if (bestMatch) {
      this.logger.debug(
        `Mapped controller '${controllerName}' to section '${bestMatch}' (score: ${bestScore})`,
      );
    } else {
      this.logger.warn(
        `No platform section found for controller '${controllerName}'`,
      );
    }

    return bestMatch;
  }

  /**
   * Get all active platform sections
   */
  async getActivePlatformSections(): Promise<CachedPlatformSection[]> {
    const sections = await this.getCachedPlatformSections();
    return Array.from(sections.values()).filter((section) => section.isActive);
  }

  /**
   * Validate if a section exists and is active
   */
  async validateSection(sectionName: string): Promise<boolean> {
    const sections = await this.getCachedPlatformSections();
    const section = sections.get(sectionName.toLowerCase());
    return section ? section.isActive : false;
  }

  /**
   * Fetch platform sections from database
   */
  private async fetchPlatformSections(): Promise<void> {
    try {
      const sections = await this.platformSectionModel.findAll({
        attributes: ['id', 'name'],
        // Remove the where clause initially to get all sections, then filter
      });

      this.platformSectionsCache.clear();
      this.sectionNameMappingCache.clear();

      sections.forEach((section) => {
        const cacheData: CachedPlatformSection = {
          id: section.id,
          name: section.name,
          displayName: (section as any).displayName || section.name,
          description: (section as any).description,
          isActive: (section as any).isActive !== false, // Default to true if not specified
        };

        this.platformSectionsCache.set(section.name.toLowerCase(), cacheData);
      });

      this.platformSectionsLastFetched = new Date();

      this.logger.debug(`Cached ${sections.length} platform sections`);

      // Auto cleanup sections cache
      setTimeout(() => {
        this.platformSectionsCache.clear();
        this.sectionNameMappingCache.clear();
        this.platformSectionsLastFetched = null;
      }, this.SECTIONS_CACHE_TTL);
    } catch (error) {
      this.logger.error('Error fetching platform sections:', error);
    }
  }

  /**
   * Fetch user data from database with optimized query
   */
  private async fetchUserData(userId: number): Promise<CachedUserData | null> {
    try {
      const userRole = await this.userRoleModel.findOne({
        where: { userId },
        include: [
          {
            model: Role,
            include: [Company],
          },
        ],
      });

      if (!userRole?.role) {
        return null;
      }

      // Fetch user's employment details to get the correct company ID
      const employmentDetails =
        await this.crudService.findOne<EmploymentDetails>(
          this.employmentDetailsModel,
          {
            where: { userId },
          },
        );

      // Fetch all permissions for this role
      const permissions = await this.permissionModel.findAll({
        where: { roleId: userRole.roleId },
        include: [PlatformSection],
      });

      // Create permission map for fast lookups
      const permissionMap = new Map<string, IPermission>();
      permissions.forEach((permission) => {
        const key = permission.platformSection.name.toLowerCase();
        permissionMap.set(key, {
          id: permission.id,
          platformSectionId: permission.platformSectionId,
          platformSectionName: permission.platformSection.name,
          canView: permission.canView,
          canCreate: permission.canCreate,
          canEdit: permission.canEdit,
          canDelete: permission.canDelete,
        });
      });

      const companyId = employmentDetails?.companyId || undefined;

      const returnData = {
        userId,
        roleId: userRole.roleId,
        roleName: userRole.role.name,
        scope: userRole.role.scope,
        companyId,
        permissions: permissionMap,
        lastUpdated: new Date(),
      };

      return returnData;
    } catch (error) {
      this.logger.error(
        `Error fetching user data for userId ${userId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Check if user has permission - optimized version
   */
  async checkPermission(
    userId: number,
    section: string,
    action: string,
    companyId?: number,
  ): Promise<boolean> {
    // Platform Super admin check - bypass all other checks
    const isPlatformSuperAdmin = await this.isUserPlatformSuperAdmin(userId);
    if (isPlatformSuperAdmin) {
      this.logger.debug(
        `Super admin ${userId} granted access to ${section}:${action}`,
      );
      return true;
    }

    const userData = await this.getCachedUserData(userId);

    if (!userData) {
      return false;
    }

    // Validate section exists in platform
    const sectionExists = await this.validateSection(section);
    if (!sectionExists) {
      this.logger.warn(`Section '${section}' does not exist or is inactive`);
      return false;
    }

    // Company scope validation
    if (companyId && userData.companyId && userData.companyId !== companyId) {
      this.logger.debug(
        `Company scope mismatch for user ${userId}: expected ${companyId}, got ${userData.companyId}`,
      );
      return false;
    }

    // Get permission for section
    const sectionPermission = userData.permissions.get(section.toLowerCase());
    if (!sectionPermission) {
      this.logger.debug(
        `No permission found for section ${section} for user ${userId}`,
      );
      return false;
    }

    // Check specific action
    const hasPermission = this.checkActionPermission(sectionPermission, action);

    if (!hasPermission) {
      this.logger.debug(
        `Permission denied for user ${userId} on ${section}:${action}`,
      );
    }

    return hasPermission;
  }

  /**
   * Check specific action permission
   */
  private checkActionPermission(
    permission: IPermission,
    action: string,
  ): boolean {
    switch (action.toLowerCase()) {
      case PERMISSION_ACTION_ENUM.VIEW.toLowerCase():
      case 'canview':
      case 'view':
        return permission.canView;

      case PERMISSION_ACTION_ENUM.CREATE.toLowerCase():
      case 'cancreate':
      case 'create':
        return permission.canCreate;

      case PERMISSION_ACTION_ENUM.EDIT.toLowerCase():
      case 'canedit':
      case 'edit':
      case 'update':
        return permission.canEdit;

      case PERMISSION_ACTION_ENUM.DELETE.toLowerCase():
      case 'candelete':
      case 'delete':
        return permission.canDelete;

      default:
        this.logger.warn(`Unknown permission action: ${action}`);
        return false;
    }
  }

  /**
   * Check if user has platform scope (Super Admin)
   */
  async hasPlatformScope(userId: number): Promise<boolean> {
    return await this.isUserPlatformSuperAdmin(userId);
  }

  /**
   * Get user's company ID
   */
  async getUserCompanyId(userId: number): Promise<number | null> {
    const userData = await this.getCachedUserData(userId);
    return userData?.companyId || null;
  }

  /**
   * Check multiple permissions at once - optimized
   */
  async checkMultiplePermissions(
    userId: number,
    permissionChecks: IPermissionCheck[],
  ): Promise<{ [key: string]: boolean }> {
    const results: { [key: string]: boolean } = {};

    // If Platform Super Admin, return all true
    const isPlatformSuperAdmin = await this.isUserPlatformSuperAdmin(userId);
    if (isPlatformSuperAdmin) {
      permissionChecks.forEach((check) => {
        const key = `${check.section}:${check.action}`;
        results[key] = true;
      });
      return results;
    }

    const userData = await this.getCachedUserData(userId);

    if (!userData) {
      // Return all false if user data not found
      permissionChecks.forEach((check) => {
        const key = `${check.section}:${check.action}`;
        results[key] = false;
      });
      return results;
    }

    // Check each permission
    for (const check of permissionChecks) {
      const key = `${check.section}:${check.action}`;

      // Validate section exists
      const sectionExists = await this.validateSection(check.section);
      if (!sectionExists) {
        results[key] = false;
        continue;
      }

      // Company scope validation
      if (
        check.companyId &&
        userData.companyId &&
        userData.companyId !== check.companyId
      ) {
        results[key] = false;
        continue;
      }

      const sectionPermission = userData.permissions.get(
        check.section.toLowerCase(),
      );
      if (!sectionPermission) {
        results[key] = false;
        continue;
      }

      results[key] = this.checkActionPermission(
        sectionPermission,
        check.action,
      );
    }

    return results;
  }

  /**
   * Get all sections user has access to
   */
  async getUserAccessibleSections(userId: number): Promise<string[]> {
    const isPlatformSuperAdmin = await this.isUserPlatformSuperAdmin(userId);
    if (isPlatformSuperAdmin) {
      try {
        const activeSections = await this.getActivePlatformSections();
        return activeSections.map((section) => section.name);
      } catch (error) {
        this.logger.error('Error fetching all platform sections:', error);
        return [];
      }
    }

    const userData = await this.getCachedUserData(userId);

    if (!userData) {
      return [];
    }

    // Return sections where user has view permission
    const accessibleSections: string[] = [];
    userData.permissions.forEach((permission) => {
      if (permission.canView) {
        accessibleSections.push(permission.platformSectionName);
      }
    });

    return accessibleSections;
  }

  /**
   * Get user permissions for API response
   */
  async getUserPermissions(userId: number): Promise<IUserPermissions | null> {
    const userData = await this.getCachedUserData(userId);

    if (!userData) {
      return null;
    }

    const permissions = Array.from(userData.permissions.values());

    return {
      userId,
      roleId: userData.roleId,
      roleName: userData.roleName,
      scope: userData.scope,
      companyId: userData.companyId,
      permissions,
    };
  }

  /**
   * Clear cache for user (use when permissions change)
   */
  clearUserCache(userId: number): void {
    this.userCache.delete(userId);
    this.logger.debug(`Cleared cache for user ${userId}`);
  }

  /**
   * Clear all cache (use when roles/permissions are updated globally)
   */
  clearAllCache(): void {
    this.userCache.clear();
    this.platformSectionsCache.clear();
    this.sectionNameMappingCache.clear();
    this.platformSectionsLastFetched = null;
    this.logger.debug('Cleared all permission cache');
  }

  /**
   * Clear platform sections cache (use when sections are updated)
   */
  clearSectionsCache(): void {
    this.platformSectionsCache.clear();
    this.sectionNameMappingCache.clear();
    this.platformSectionsLastFetched = null;
    this.logger.debug('Cleared platform sections cache');
  }

  /**
   * Check if cache is still valid
   */
  private isCacheValid(lastUpdated: Date): boolean {
    return Date.now() - lastUpdated.getTime() < this.CACHE_TTL;
  }

  /**
   * Check if sections cache is still valid
   */
  private isSectionsCacheValid(lastUpdated: Date): boolean {
    return Date.now() - lastUpdated.getTime() < this.SECTIONS_CACHE_TTL;
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): {
    userCache: { size: number; ttl: number };
    sectionsCache: { size: number; ttl: number };
    mappingCache: { size: number };
  } {
    return {
      userCache: {
        size: this.userCache.size,
        ttl: this.CACHE_TTL,
      },
      sectionsCache: {
        size: this.platformSectionsCache.size,
        ttl: this.SECTIONS_CACHE_TTL,
      },
      mappingCache: {
        size: this.sectionNameMappingCache.size,
      },
    };
  }
}
