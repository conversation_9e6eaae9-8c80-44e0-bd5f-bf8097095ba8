import { Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Permission } from 'src/models/roles-and-permissions/permission.model';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { PlatformSection } from 'src/models/roles-and-permissions/platform-section.model';
import { Company } from 'src/models/company.model';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { AdminPermissionsController } from './controllers/admin.permissions.controller';
import { PermissionsController } from './controllers/permissions.controller';
import { PermissionService } from './permission.service';
import { CrudHelperService } from 'src/common/crud-helper/crud-helper.service';
import { RolesModule } from '../roles/roles.module';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Permission,
      UserRole,
      Role,
      PlatformSection,
      Company,
      EmploymentDetails,
    ]),
    forwardRef(() => RolesModule),
  ],
  controllers: [AdminPermissionsController, PermissionsController],
  providers: [PermissionService, CrudHelperService],
  exports: [PermissionService],
})
export class PermissionsModule {}
