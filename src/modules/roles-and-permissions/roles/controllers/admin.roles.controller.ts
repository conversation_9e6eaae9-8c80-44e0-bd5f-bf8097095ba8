import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { RolesService } from '../roles.service';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { AdminRequestI } from 'src/modules/auth/auth.interfaces';
import { AdminFindAllRolesDto } from '../dto/admin.find.all.dto';
import { SetPermissionsDto } from '../dto/set-permissions.dto';
import {
  CanCreatePlatform,
  CanDeletePlatform,
  CanViewPlatform,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { CanEditPlatform } from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/enums';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth.guard';

@Controller('admin/roles')
@UseGuards(JwtAuthGuard)
// @UseGuards(PermissionGuard)
export class AdminRolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Get()
  // @CanViewPlatform(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  findAll(@Query() dto: AdminFindAllRolesDto, @Req() req: AdminRequestI) {
    return this.rolesService.findAll(dto, req.user);
  }

  @Get('paginated')
  // @CanViewPlatform(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  findAllPaginated(@Query() dto: AdminFindAllRolesDto) {
    return this.rolesService.findAllPaginated(dto);
  }

  @Get(':id')
  // @CanViewPlatform(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  findById(@Param('id', ParseIntPipe) id: number) {
    return this.rolesService.findById(id);
  }

  @Post()
  // @CanCreatePlatform(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  create(@Body() dto: Partial<Role>) {
    return this.rolesService.create(dto);
  }

  @Put(':id')
  // @CanEditPlatform(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  update(@Param('id', ParseIntPipe) id: number, @Body() dto: Partial<Role>) {
    return this.rolesService.update(id, dto);
  }

  @Post(':id/permissions')
  // @CanEditPlatform(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  setPermissions(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: SetPermissionsDto,
  ) {
    return this.rolesService.setPermissions(id, dto);
  }

  @Delete(':id')
  // @CanDeletePlatform(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.rolesService.delete(id);
  }
}
