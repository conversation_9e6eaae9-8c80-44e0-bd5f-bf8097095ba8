import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { Permission } from 'src/models/roles-and-permissions/permission.model';
import { PlatformSection } from 'src/models/roles-and-permissions/platform-section.model';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { CompanyService } from 'src/modules/company/services/company.service';
import { PermissionService } from '../permissions/permission.service';
import { AdminFindAllRolesDto } from './dto/admin.find.all.dto';
import { UserFindAllRolesDto } from './dto/user.find.all.dto';
import { SetPermissionsDto } from './dto/set-permissions.dto';
import { ROLES_ENUM } from './utils/enums';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';

@Injectable()
export class RolesService {
  private superAdminRoleCache: Partial<Role> | null = null;
  private superAdminRoleCacheTime: Date | null = null;
  private readonly SUPER_ADMIN_CACHE_TTL = 30 * 60 * 1000; // 30 minutes

  constructor(
    @InjectModel(Role) private readonly roleModel: typeof Role,
    @InjectModel(Permission)
    private readonly permissionModel: typeof Permission,
    @InjectModel(UserRole)
    private readonly userRoleModel: typeof UserRole,
    @InjectModel(EmploymentDetails)
    private readonly employmentDetailsModel: typeof EmploymentDetails,
    private readonly crudService: CrudHelperService,
    private readonly companyService: CompanyService,
  ) {}

  /**
   * Get the Super Admin role (cached for performance)
   */
  async getSuperAdminRole(): Promise<Partial<Role> | null> {
    // Check if cache is valid
    if (
      this.superAdminRoleCache &&
      this.superAdminRoleCacheTime &&
      Date.now() - this.superAdminRoleCacheTime.getTime() <
        this.SUPER_ADMIN_CACHE_TTL
    ) {
      return this.superAdminRoleCache;
    }

    try {
      // Fetch Super Admin role from database
      const superAdminRole = await this.crudService.findOne<Role>(
        this.roleModel,
        {
          where: {
            name: ROLES_ENUM.PLATFORM_SUPER_ADMIN,
            scope: ROLE_SCOPE_ENUM.PLATFORM,
          },
        },
      );

      // Cache the result
      this.superAdminRoleCache = superAdminRole;
      this.superAdminRoleCacheTime = new Date();

      // Auto cleanup cache
      setTimeout(() => {
        this.superAdminRoleCache = null;
        this.superAdminRoleCacheTime = null;
      }, this.SUPER_ADMIN_CACHE_TTL);

      return superAdminRole;
    } catch (error) {
      console.error('Error fetching Super Admin role:', error);
      return null;
    }
  }

  /**
   * Check if user is Super Admin by comparing role IDs
   */
  async isUserSuperAdmin(userId: number): Promise<boolean> {
    try {
      // Get Super Admin role
      const superAdminRole = await this.getSuperAdminRole();
      if (!superAdminRole) {
        return false;
      }

      // Get user's role
      const userRole = await this.crudService.findOne<UserRole>(
        this.userRoleModel,
        {
          where: { userId },
          include: [{ model: Role }],
        },
      );

      if (!userRole?.role) {
        return false;
      }

      // Compare role IDs
      return userRole.roleId === superAdminRole.id;
    } catch (error) {
      console.error(`Error checking if user ${userId} is Super Admin:`, error);
      return false;
    }
  }

  /**
   * Get user's role ID
   */
  async getUserRoleId(userId: number): Promise<number | null> {
    try {
      const userRole = await this.crudService.findOne<UserRole>(
        this.userRoleModel,
        {
          where: { userId },
        },
      );

      return userRole?.roleId || null;
    } catch (error) {
      console.error(`Error getting role ID for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Clear Super Admin role cache (use when roles are updated)
   */
  clearSuperAdminCache(): void {
    this.superAdminRoleCache = null;
    this.superAdminRoleCacheTime = null;
  }

  async create(data: Partial<Role>, user?: RequestUserObjectI) {
    // if user role socpe is company than get the company id from the employemt details of the user and add company id to data
    if (user?.scope === ROLE_SCOPE_ENUM.COMPANY) {
      const employmentDetails = await this.employmentDetailsModel.findOne({
        where: { userId: user.id },
      });
      data.companyId = employmentDetails.companyId;
    }

    return this.crudService.create(this.roleModel, data);
  }

  async findAllByUser(user: RequestUserObjectI) {
    const company = await this.companyService.findByUserId(user.id);

    return this.crudService.findAll(this.roleModel, {
      where: {
        companyId: company.id,
        scope: ROLE_SCOPE_ENUM.COMPANY,
      },
    });
  }

  async findAllByUserPaginated(
    user: RequestUserObjectI,
    dto: UserFindAllRolesDto,
  ): Promise<PaginatedResult<Partial<Role>>> {
    const company = await this.companyService.findByUserId(user.id);

    return this.crudService.paginateWithQuery<Role>(this.roleModel, {
      page: dto.page,
      limit: dto.limit,
      where: {
        companyId: company.id,
        scope: ROLE_SCOPE_ENUM.COMPANY,
      },
      order: [['createdAt', 'DESC']],
    });
  }

  async findAll(dto: AdminFindAllRolesDto, user: RequestUserObjectI) {
    const where: any = {};

    // If user scope is company, filter roles by user's company
    if (user.scope === ROLE_SCOPE_ENUM.COMPANY) {
      const company = await this.companyService.findByUserId(user.id);
      where.companyId = company.id;
      where.scope = ROLE_SCOPE_ENUM.COMPANY;
    } else {
      // For platform scope users, use DTO filters
      if (dto.companyId) {
        where.companyId = dto.companyId;
      }

      if (dto.scope) {
        where.scope = dto.scope;
      }
    }

    return this.crudService.findAll(this.roleModel, {
      where,
    });
  }

  async findAllPaginated(
    dto: AdminFindAllRolesDto,
  ): Promise<PaginatedResult<Partial<Role>>> {
    const where: any = {};

    if (dto.companyId) {
      where.companyId = dto.companyId;
    }

    if (dto.scope) {
      where.scope = dto.scope;
    }

    return this.crudService.paginateWithQuery<Role>(this.roleModel, {
      page: dto.page,
      limit: dto.limit,
      where,
      order: [['createdAt', 'DESC']],
    });
  }

  async findById(id: number, user?: RequestUserObjectI) {
    const where: any = { id };

    // If user is provided (company user), filter by their company
    if (user) {
      const isSuperAdmin = await this.isUserSuperAdmin(user.id);

      if (!isSuperAdmin) {
        // Company users can only access roles in their company
        const company = await this.companyService.findByUserId(user.id);
        where.companyId = company.id;
      }
    }

    const data = await this.crudService.findOne(this.roleModel, {
      where,
      include: [
        {
          model: Permission,
          include: [{ model: PlatformSection }],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    return data;
  }

  async findRoleByName(name: string): Promise<Partial<Role>> {
    return this.crudService.findOne<Role>(this.roleModel, {
      where: { name },
    });
  }

  async setPermissions(
    roleId: number,
    dto: SetPermissionsDto,
    user?: RequestUserObjectI,
  ) {
    const where: any = { id: roleId };

    // If user is provided (company user), filter by their company
    if (user) {
      const isSuperAdmin = await this.isUserSuperAdmin(user.id);

      if (!isSuperAdmin) {
        // Company users can only access roles in their company
        const company = await this.companyService.findByUserId(user.id);
        where.companyId = company.id;
      }
    }

    // Verify the role exists and user has access to it
    const role = await this.crudService.findOne(this.roleModel, { where });
    if (!role) {
      throw new Error('Role not found or access denied');
    }

    // First, delete existing permissions for this role
    await this.permissionModel.destroy({
      where: { roleId },
    });

    // Create new permissions
    const permissionsToCreate = dto.permissions.map((permission) => ({
      roleId,
      platformSectionId: permission.platformSectionId,
      canView: permission.canView || false,
      canCreate: permission.canCreate || false,
      canEdit: permission.canEdit || false,
      canDelete: permission.canDelete || false,
    }));

    if (permissionsToCreate.length > 0) {
      await this.permissionModel.bulkCreate(permissionsToCreate);
    }

    // Return the updated role with permissions using findById which now handles associations properly
    return this.findById(roleId, user);
  }

  async update(id: number, data: Partial<Role>, user?: RequestUserObjectI) {
    const where: any = { id };

    // If user is provided (company user), filter by their company
    if (user) {
      const isSuperAdmin = await this.isUserSuperAdmin(user.id);

      if (!isSuperAdmin) {
        // Company users can only update roles in their company
        const company = await this.companyService.findByUserId(user.id);
        where.companyId = company.id;
        where.scope = ROLE_SCOPE_ENUM.COMPANY;
      }
    }

    return this.crudService.update(this.roleModel, data, { where });
  }

  async delete(id: number, user?: RequestUserObjectI) {
    const where: any = { id };

    // If user is provided (company user), filter by their company
    if (user) {
      const isSuperAdmin = await this.isUserSuperAdmin(user.id);

      if (!isSuperAdmin) {
        // Company users can only delete roles in their company
        const company = await this.companyService.findByUserId(user.id);
        where.companyId = company.id;
        where.scope = ROLE_SCOPE_ENUM.COMPANY;
      }
    }

    return this.crudService.delete(this.roleModel, { where });
  }

  async getRoleByNameAndScope(name: string, scope: ROLE_SCOPE_ENUM) {
    return this.crudService.findOne<Role>(this.roleModel, {
      where: { name, scope: scope },
    });
  }
}
