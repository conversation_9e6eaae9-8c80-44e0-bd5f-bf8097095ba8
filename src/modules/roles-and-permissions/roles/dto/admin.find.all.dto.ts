import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsString, Min } from 'class-validator';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { Type } from 'class-transformer';

export class AdminFindAllRolesDto {
  @IsEnum(ROLE_SCOPE_ENUM)
  @IsOptional()
  scope?: ROLE_SCOPE_ENUM;

  @IsString()
  @IsOptional()
  companyId?: string;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  limit?: number = 10;
}
