import {
  Is<PERSON><PERSON>y,
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsOptional,
} from 'class-validator';

export class PermissionDto {
  @IsInt()
  @IsNotEmpty()
  platformSectionId: number;

  @IsBoolean()
  @IsOptional()
  canView?: boolean = false;

  @IsBoolean()
  @IsOptional()
  canCreate?: boolean = false;

  @IsBoolean()
  @IsOptional()
  canEdit?: boolean = false;

  @IsBoolean()
  @IsOptional()
  canDelete?: boolean = false;
}

export class SetPermissionsDto {
  @IsArray()
  @IsNotEmpty()
  permissions: PermissionDto[];
}
