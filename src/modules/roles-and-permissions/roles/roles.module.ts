import { Global, Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { Permission } from 'src/models/roles-and-permissions/permission.model';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { AdminRolesController } from './controllers/admin.roles.controller';
import { RolesService } from './roles.service';
import { RolesController } from './controllers/roles.controller';
import { CompanyModule } from 'src/modules/company/company.module';
import { PermissionsModule } from 'src/modules/roles-and-permissions/permissions/permissions.module';
import { UsersModule } from 'src/modules/users/users.module';

@Global()
@Module({
  imports: [
    SequelizeModule.forFeature([Role, Permission, UserRole, EmploymentDetails]),
    forwardRef(() => CompanyModule),
    forwardRef(() => PermissionsModule),
    forwardRef(() => UsersModule),
  ],
  controllers: [AdminRolesController, RolesController],
  providers: [RolesService],
  exports: [RolesService],
})
export class RolesModule {}
