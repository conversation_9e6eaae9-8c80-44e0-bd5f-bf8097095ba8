import { Controller, Get, Param, ParseIntPipe } from '@nestjs/common';
import { PlatformSectionsService } from '../platform-sections.service';

@Controller('platform-sections')
export class PlatformSectionsController {
  constructor(
    private readonly platformSectionsService: PlatformSectionsService,
  ) {}

  @Get()
  findAll() {
    return this.platformSectionsService.findAll();
  }

  @Get(':id')
  findById(@Param('id', ParseIntPipe) id: number) {
    return this.platformSectionsService.findById(id);
  }
}
