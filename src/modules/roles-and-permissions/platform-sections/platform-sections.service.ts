import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { PlatformSection } from 'src/models/roles-and-permissions/platform-section.model';
import { Permission } from 'src/models/roles-and-permissions/permission.model';

@Injectable()
export class PlatformSectionsService {
  constructor(
    @InjectModel(PlatformSection)
    private readonly platformSectionModel: typeof PlatformSection,
    private readonly crudService: CrudHelperService,
  ) {}

  create(data: Partial<PlatformSection>) {
    return this.crudService.create(this.platformSectionModel, data);
  }

  findAll() {
    return this.crudService.findAll(this.platformSectionModel);
  }

  findById(id: number) {
    return this.crudService.findOne(this.platformSectionModel, {
      where: { id },
    });
  }

  update(id: number, data: Partial<PlatformSection>) {
    return this.crudService.update(this.platformSectionModel, data, {
      where: { id },
    });
  }

  delete(id: number) {
    return this.crudService.delete(this.platformSectionModel, {
      where: { id },
    });
  }
}
