import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { PlatformSection } from 'src/models/roles-and-permissions/platform-section.model';
import { AdminPlatformSectionsController } from './controllers/admin.platform-sections.controller';
import { PlatformSectionsController } from './controllers/platform-sections.controller';
import { PlatformSectionsService } from './platform-sections.service';

@Module({
  imports: [SequelizeModule.forFeature([PlatformSection])],
  controllers: [AdminPlatformSectionsController, PlatformSectionsController],
  providers: [PlatformSectionsService],
  exports: [PlatformSectionsService],
})
export class PlatformSectionsModule {}
