import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { UpdateUserRoleDto } from './dto/update-role.dto';
import { User } from 'src/models/users-models/user.model';
import { Transaction } from 'sequelize';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { Permission } from 'src/models/roles-and-permissions/permission.model';
import { PlatformSection } from 'src/models/roles-and-permissions/platform-section.model';

@Injectable()
export class UserRoleService {
  constructor(
    @InjectModel(UserRole) private readonly userRoleModel: typeof UserRole,
    @InjectModel(Role) private readonly roleModel: typeof Role,
    private readonly crudService: CrudHelperService,
  ) {}

  async assignRole(userId: number, roleId: number): Promise<UserRole> {
    // First, validate that the role exists
    const roleExists = await this.roleModel.findByPk(roleId);
    if (!roleExists) {
      throw new NotFoundException(`Role with ID ${roleId} does not exist`);
    }

    const existing = await this.userRoleModel.findOne({ where: { userId } });

    if (existing) {
      existing.roleId = roleId;
      await existing.save();
      return existing;
    }

    return this.userRoleModel.create({ userId, roleId });
  }

  async assignRoleWithTransaction(
    userId: number,
    roleId: number,
    transaction: Transaction,
  ): Promise<UserRole> {
    // First, validate that the role exists
    const roleExists = await this.roleModel.findByPk(roleId, { transaction });
    if (!roleExists) {
      throw new NotFoundException(`Role with ID ${roleId} does not exist`);
    }

    const existing = await this.userRoleModel.findOne({
      where: { userId },
      transaction,
    });

    if (existing) {
      existing.roleId = roleId;
      await existing.save({ transaction });
      return existing;
    }

    return this.userRoleModel.create({ userId, roleId }, { transaction });
  }

  async getRoleForUser(userId: number): Promise<Partial<UserRole>> {
    const response = await this.crudService.findOne<UserRole>(UserRole, {
      where: { userId },
      include: [
        {
          model: Role,
        },
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName', 'email', 'profileImage'],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    return response;
  }

  async getAllPaginated(
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<UserRole>>> {
    const data = await this.crudService.paginateWithQuery<UserRole>(
      this.userRoleModel,
      {
        page,
        limit,
        include: [
          {
            model: Role,
          },
          {
            model: User,
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'profileImage',
            ],
          },
        ],
      },
    );

    return data;
  }

  async findMyRoleAndPermissions(user: RequestUserObjectI) {
    const userRole = await this.crudService.findOne<UserRole>(
      this.userRoleModel,
      {
        where: {
          userId: user.id,
        },
        include: [
          {
            model: Role,
          },
        ],
      },
    );

    if (!userRole || !userRole.role) {
      throw new NotFoundException(`No role found for user ${user.id}`);
    }

    const data = await this.crudService.findOne(this.roleModel, {
      where: { id: userRole.role.id },
      include: [
        {
          model: Permission,
          include: [{ model: PlatformSection }],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    return data;
  }

  async findUserById(userId: number): Promise<Partial<UserRole>> {
    return this.crudService.findOne<UserRole>(this.userRoleModel, {
      where: { userId },
      include: [
        {
          model: Role,
        },
      ],
    });
  }

  delete(id: number) {
    return this.crudService.delete(this.userRoleModel, { where: { id } });
  }
}
