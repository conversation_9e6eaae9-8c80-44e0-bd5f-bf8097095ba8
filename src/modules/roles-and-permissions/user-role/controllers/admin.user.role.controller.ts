import {
  Controller,
  Get,
  Param,
  Body,
  Delete,
  Put,
  ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import { UserRoleService } from '../user-role.service';
import { AssignRoleDto } from '../dto/assign-role.dto';
import { RequirePermission } from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/enums';

@Controller('admin/user-roles')
export class AdminUserRoleController {
  constructor(private readonly userRoleService: UserRoleService) {}

  @Put('assign')
  // @RequirePermission(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  assignRole(@Body() body: AssignRoleDto) {
    return this.userRoleService.assignRole(body.userId, body.roleId);
  }

  @Get('user/:userId')
  // @RequirePermission(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  getRoleForUser(@Param('userId', ParseIntPipe) userId: number) {
    return this.userRoleService.getRoleForUser(userId);
  }

  @Get('paginate')
  // @RequirePermission(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  async getAllPaginated(@Query('page') page = 1, @Query('limit') limit = 10) {
    return this.userRoleService.getAllPaginated(Number(page), Number(limit));
  }

  @Delete(':id')
  @RequirePermission(PLATFORM_SECTION_ENUM.ROLES_AND_PERMISSIONS)
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.userRoleService.delete(id);
  }
}
