import {
  Controller,
  Get,
  Param,
  Body,
  Delete,
  Put,
  ParseIntPipe,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import { UserRoleService } from '../user-role.service';
import { AssignRoleDto } from '../dto/assign-role.dto';
import {
  AuthenticatedOnly,
  CanCreate,
  CanDelete,
  CanView,
  RequirePermission,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/enums';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';

@Controller('user-roles')
@UseGuards(PermissionGuard)
export class UserRoleController {
  constructor(private readonly userRoleService: UserRoleService) {}

  @Put('assign')
  @CanCreate(PLATFORM_SECTION_ENUM.SETTINGS)
  assignRole(@Body() body: AssignRoleDto) {
    return this.userRoleService.assignRole(body.userId, body.roleId);
  }

  @Get('user/:userId')
  @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  getRoleForUser(@Param('userId', ParseIntPipe) userId: number) {
    return this.userRoleService.getRoleForUser(userId);
  }

  @Get('me')
  @AuthenticatedOnly()
  findMyRole(@Req() req: UserRequestI) {
    return this.userRoleService.findMyRoleAndPermissions(req.user);
  }

  @Get('paginate')
  @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  async getAllPaginated(@Query('page') page = 1, @Query('limit') limit = 10) {
    return this.userRoleService.getAllPaginated(Number(page), Number(limit));
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.SETTINGS)
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.userRoleService.delete(id);
  }
}
