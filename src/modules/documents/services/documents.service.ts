import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Document } from '../../../models/document.model';
import { CreateDocumentDto } from '../dto/create-document.dto';
import { UpdateDocumentDto } from '../dto/update-document.dto';

@Injectable()
export class DocumentsService {
  constructor(
    @InjectModel(Document)
    private documentModel: typeof Document,
  ) {}

  async create(createDocumentDto: CreateDocumentDto): Promise<Document> {
    return this.documentModel.create({ ...createDocumentDto } as any);
  }

  async findAll(): Promise<Document[]> {
    return this.documentModel.findAll({
      include: { all: true },
    });
  }

  async findByEmployeeId(employeeId: number): Promise<Document[]> {
    return this.documentModel.findAll({
      where: { employeeId },
      include: { all: true },
    });
  }

  async findByType(type: string): Promise<Document[]> {
    return this.documentModel.findAll({
      where: { type },
      include: { all: true },
    });
  }

  async findOne(id: number): Promise<Document> {
    const document = await this.documentModel.findByPk(id, {
      include: { all: true },
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    return document;
  }

  async update(
    id: number,
    updateDocumentDto: UpdateDocumentDto,
  ): Promise<Document> {
    const document = await this.findOne(id);
    await document.update(updateDocumentDto as any);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const document = await this.findOne(id);
    await document.destroy();
  }

  async findExpiring(days: number = 30): Promise<Document[]> {
    const today = new Date();
    const expiryThreshold = new Date();
    expiryThreshold.setDate(today.getDate() + days);

    return this.documentModel.findAll({
      where: {
        expiryDate: {
          [Symbol.for('lt')]: expiryThreshold,
          [Symbol.for('gt')]: today,
        },
      },
      include: { all: true },
    });
  }

  async findExpired(): Promise<Document[]> {
    const today = new Date();

    return this.documentModel.findAll({
      where: {
        expiryDate: {
          [Symbol.for('lt')]: today,
        },
      },
      include: { all: true },
    });
  }
}
