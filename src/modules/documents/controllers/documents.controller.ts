import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { DocumentsService } from '../services/documents.service';
import { Document } from '../../../models/document.model';
import { CreateDocumentDto } from '../dto/create-document.dto';
import { UpdateDocumentDto } from '../dto/update-document.dto';
// import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
// import { FileInterceptor } from '@nestjs/platform-express';

@Controller('documents')
// @UseGuards(JwtAuthGuard)
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post()
  // @UseInterceptors(FileInterceptor('file'))
  async create(
    @Body() createDocumentDto: CreateDocumentDto,
    // @UploadedFile() file: Express.Multer.File
  ): Promise<Document> {
    // Handle file upload here if needed
    return this.documentsService.create(createDocumentDto);
  }

  @Get()
  async findAll(@Query('type') type?: string): Promise<Document[]> {
    if (type) {
      return this.documentsService.findByType(type);
    }
    return this.documentsService.findAll();
  }

  @Get('employee/:employeeId')
  async findByEmployeeId(
    @Param('employeeId') employeeId: number,
  ): Promise<Document[]> {
    return this.documentsService.findByEmployeeId(employeeId);
  }

  @Get('expiring')
  async findExpiring(@Query('days') days: number = 30): Promise<Document[]> {
    return this.documentsService.findExpiring(days);
  }

  @Get('expired')
  async findExpired(): Promise<Document[]> {
    return this.documentsService.findExpired();
  }

  @Get(':id')
  async findOne(@Param('id') id: number): Promise<Document> {
    return this.documentsService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateDocumentDto: UpdateDocumentDto,
  ): Promise<Document> {
    return this.documentsService.update(id, updateDocumentDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: number): Promise<void> {
    return this.documentsService.remove(id);
  }
}
