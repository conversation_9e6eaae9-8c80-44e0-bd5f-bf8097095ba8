import { PartialType } from '@nestjs/mapped-types';
import { CreateDocumentDto } from './create-document.dto';
import {
  IsString,
  IsNumber,
  IsOptional,
  IsEnum,
  IsDate,
  IsUrl,
} from 'class-validator';
import { Type } from 'class-transformer';

enum DocumentType {
  CONTRACT = 'contract',
  ID = 'identification',
  CERTIFICATE = 'certificate',
  POLICY = 'policy',
  OTHER = 'other',
}

export class UpdateDocumentDto extends PartialType(CreateDocumentDto) {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  filename?: string;

  @IsOptional()
  @IsEnum(DocumentType)
  type?: string;

  @IsOptional()
  @IsNumber()
  employeeId?: number;

  @IsOptional()
  @IsString()
  @IsUrl()
  fileUrl?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  uploadDate?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  expiryDate?: Date;
}
