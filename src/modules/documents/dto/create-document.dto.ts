import {
  IsString,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON>ptional,
  IsEnum,
  IsDate,
  IsUrl,
} from 'class-validator';
import { Type } from 'class-transformer';

enum DocumentType {
  CONTRACT = 'contract',
  ID = 'identification',
  CERTIFICATE = 'certificate',
  POLICY = 'policy',
  OTHER = 'other',
}

export class CreateDocumentDto {
  @IsString()
  name: string;

  @IsString()
  filename: string;

  @IsEnum(DocumentType)
  type: string;

  @IsOptional()
  @IsNumber()
  employeeId?: number;

  @IsString()
  @IsUrl()
  fileUrl: string;

  @IsOptional()
  @IsString()
  description?: string;

  @Type(() => Date)
  @IsDate()
  uploadDate: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  expiryDate?: Date;
}
