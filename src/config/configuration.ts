export default () => ({
  port: parseInt(process.env.PORT || '3001', 10),

  database: {
    url: process.env.DATABASE_URL ?? null,
    host: process.env.PGHOST || 'localhost',
    port: parseInt(process.env.PGPORT || '5432', 10),
    username: process.env.PGUSER || 'postgres',
    password: process.env.PGPASSWORD || 'my_secure_password',
    database: process.env.PGDATABASE || 'hr_master',
  },

  jwt: {
    secret: process.env.JWT_SECRET || 'secret-key',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'refresh-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
  },

  email: {
    sendgridApiKey: process.env.SENDGRID_API_KEY,
    defaultFromEmail:
      process.env.DEFAULT_FROM_EMAIL || '<EMAIL>',
  },

  admin: {
    email: process.env.DEFAULT_ADMIN_EMAIL ?? '<EMAIL>',
    username: process.env.DEFAULT_USERNAME ?? 'admin',
    password: process.env.DEFAULT_ADMIN_PASSWORD ?? 'Admin@123',
  },
});
