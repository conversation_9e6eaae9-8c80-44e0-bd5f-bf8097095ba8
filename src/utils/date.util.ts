export function getDateRange(month: number, year: number) {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0, 23, 59, 59, 999);
  return { startDate, endDate };
}

export function getDaysInMonth(month: number, year: number) {
  const date = new Date(year, month, 0);
  return date.getDate();
}

export function isLateClockIn(clockInTime: Date): boolean {
  const officeStart = new Date(clockInTime);
  officeStart.setHours(9, 0, 0, 0); // 9:00 AM
  return clockInTime.getTime() > officeStart.getTime();
}

export function isToday(date: Date): boolean {
  const today = new Date();
  return (
    today.getDate() === date.getDate() &&
    today.getMonth() === date.getMonth() &&
    today.getFullYear() === date.getFullYear()
  );
}
