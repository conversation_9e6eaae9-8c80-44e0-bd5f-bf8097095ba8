export enum GENDER_TYPES_ENUM {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

export enum MARITAL_STATUS_ENUM {
  SINGLE = 'single',
  MARRIED = 'married',
  WIDOWED = 'widowed',
}

export enum USER_ACCOUNT_STATUS_ENUM {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  BLOCKED = 'blocked',
  PENDING = 'pending',
  VERIFIED = 'verified',
  DELETED = 'deleted',
}

export enum ROLE_SCOPE_ENUM {
  PLATFORM = 'platform',
  COMPANY = 'company',
}

// Permission Action Enums
export enum PERMISSION_ACTION_ENUM {
  VIEW = 'canView',
  CREATE = 'canCreate',
  EDIT = 'canEdit',
  DELETE = 'canDelete',
}

// Platform Section Enums (matching the constants)
export enum PLATFORM_SECTION_ENUM {
  DASHBOARD = 'dashboard',
  USERS = 'users',
  EMPLOYEES = 'employees',
  TIME_ATTENDANCE = 'time-attendance',
  PAYROLLS = 'payrolls',
  LEAVE_MANAGEMENT = 'leave-management',
  DOCUMENTS = 'documents',
  PERFORMANCE = 'performance',
  PROJECT_BONUSES = 'project-bonuses',
  CHAT = 'chat',
  AI_ASSISTANCE = 'ai-assistance',
  SETTINGS = 'settings',
  ROLES_AND_PERMISSIONS = 'roles-and-permissions',
}

// HTTP Method to Permission Action mapping
export enum HTTP_METHOD_PERMISSION_MAP {
  GET = PERMISSION_ACTION_ENUM.VIEW,
  POST = PERMISSION_ACTION_ENUM.CREATE,
  PUT = PERMISSION_ACTION_ENUM.EDIT,
  PATCH = PERMISSION_ACTION_ENUM.EDIT,
  DELETE = PERMISSION_ACTION_ENUM.DELETE,
}
