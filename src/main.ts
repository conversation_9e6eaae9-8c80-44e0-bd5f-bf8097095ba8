import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { WebSocketIoAdapter } from './websocket.adapter';
import { AllExceptionsFilter } from './common/filters/http-exception.filter';
import * as dotenv from 'dotenv';

dotenv.config();

async function bootstrap() {
  try {
    const app = await NestFactory.create(AppModule);
    const logger = new Logger('Bootstrap');

    // Get config service
    const configService = app.get(ConfigService);

    // Enable CORS
    app.enableCors({
      origin: true,
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      credentials: true,
    });

    // Add global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
      }),
    );

    // Set global prefix for all routes
    app.setGlobalPrefix('api');

    // Setup WebSocket adapter
    const webSocketAdapter = new WebSocketIoAdapter(app);
    app.useWebSocketAdapter(webSocketAdapter);

    app.useGlobalFilters(new AllExceptionsFilter());

    // Start the server
    const port = configService.get('PORT', process.env.PORT);
    await app.listen(port);
    logger.log(`Application is running on: ${await app.getUrl()}`);
  } catch (error) {
    const logger = new Logger('Bootstrap');
    logger.error('Error starting application:', error);
    process.exit(1);
  }
}
bootstrap();
