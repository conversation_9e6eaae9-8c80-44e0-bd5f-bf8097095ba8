import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { PlatformSection } from 'src/models/roles-and-permissions/platform-section.model';
import { PANNEL_SECTIONS_NAMES_LIST } from 'src/utils/constants';

@Injectable()
export class SectionSeeder {
  constructor(
    @InjectModel(PlatformSection)
    private readonly sectionModel: typeof PlatformSection,
  ) {}

  async seed() {
    const sectionMap = new Map<string, number>();

    for (const name of PANNEL_SECTIONS_NAMES_LIST) {
      const [section] = await this.sectionModel.findOrCreate({
        where: { name },
        defaults: { name },
      });
      sectionMap.set(name, section.id);
    }
  }
}
