import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Country } from 'src/models/address-models/country-model';
import { User } from 'src/models/users-models/user.model';
import * as bcrypt from 'bcrypt';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { Company } from 'src/models/company.model';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { Department } from 'src/models/department.model';

const DEFAULT_PASSWORD = 'Admin@123';
const SALT_ROUNDS = 10;

@Injectable()
export class PlatformUserSeeder {
  private readonly logger = new Logger(PlatformUserSeeder.name);

  constructor(
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(UserRole)
    private readonly userRoleModel: typeof UserRole,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(EmploymentDetails)
    private readonly employmentDetailsModel: typeof EmploymentDetails,
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
  ) {}

  async seed(): Promise<void> {
    const hashedPassword = await this.hashPassword();
    const roles = await this.getRoles();
    const company = await this.companyModel.findOne({
      where: { name: 'Softbuilders LLC' },
    });

    // Executive Management
    const executiveManagementDepartment = await this.departmentModel.findOne({
      where: { name: 'Executive Management' },
    });

    const hrDepartment = await this.departmentModel.findOne({
      where: { name: 'Human Resources' },
    });

    const PLATFORM_USERS = [
      {
        email: '<EMAIL>',
        username: 'companysuperadmin',
        firstName: 'Company',
        lastName: 'Super Admin',
        departmentId: executiveManagementDepartment.id,
      },
      {
        email: '<EMAIL>',
        username: 'companyadmin',
        firstName: 'Company',
        lastName: 'Admin',
        departmentId: hrDepartment.id,
      },
    ];

    for (const platformUser of PLATFORM_USERS) {
      await this.createPlatformUser(
        platformUser,
        hashedPassword,
        roles,
        company.id,
        platformUser.departmentId,
      );
    }
  }

  private async hashPassword(): Promise<string> {
    return bcrypt.hash(DEFAULT_PASSWORD, SALT_ROUNDS);
  }

  private async getRoles(): Promise<{ superAdmin: Role; admin: Role }> {
    const company = await this.companyModel.findOne({
      where: { name: 'Softbuilders LLC' },
    });

    const [CompanySuperAdminRole, CompanyAdminRole] = await Promise.all([
      this.roleModel.findOne({
        where: {
          name: 'Company Super Admin',
          scope: ROLE_SCOPE_ENUM.COMPANY,
          companyId: company.id,
        },
      }),
      this.roleModel.findOne({
        where: {
          name: 'Company Admin',
          scope: ROLE_SCOPE_ENUM.COMPANY,
          companyId: company.id,
        },
      }),
    ]);

    if (!CompanySuperAdminRole || !CompanyAdminRole) {
      throw new Error('Required roles not found');
    }

    return { superAdmin: CompanySuperAdminRole, admin: CompanyAdminRole };
  }

  private async createPlatformUser(
    adminUser: Partial<User>,
    hashedPassword: string,
    roles: { superAdmin: Role; admin: Role },
    companyId: number,
    departmentId: number,
  ): Promise<void> {
    const [user] = await this.userModel.findOrCreate({
      where: { email: adminUser.email },
      defaults: {
        ...adminUser,
        password: hashedPassword,
      },
    });

    const role =
      adminUser.email === '<EMAIL>'
        ? roles.superAdmin
        : roles.admin;

    await this.userRoleModel.findOrCreate({
      where: { userId: user.id, roleId: role.id },
    });

    await this.employmentDetailsModel.create({
      userId: user.id,
      companyId: companyId,
      departmentId: departmentId,
    });

    this.logger.log(`Created/Updated company user: ${adminUser.email}`);
  }
}
