import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Department } from 'src/models/department.model';

@Injectable()
export class DepartmentSeeder {
  constructor(
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
  ) {}

  async seed() {
    const departments = [
      {
        name: 'Executive Management',
        description: 'Oversees overall company operations and strategy',
      },
      {
        name: 'Human Resources',
        description: 'Manages employee-related services',
      },
      {
        name: 'Technical Staff',
        description: 'Handles all technical development',
      },
      { name: 'Marketing', description: 'Responsible for brand and outreach' },
      { name: 'Finance', description: 'Handles financial records and reports' },
    ];

    for (const dept of departments) {
      await this.departmentModel.findOrCreate({
        where: { name: dept.name },
        defaults: dept,
      });
    }
  }
}
