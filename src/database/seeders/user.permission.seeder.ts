import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { Permission } from 'src/models/roles-and-permissions/permission.model';
import { PlatformSection } from 'src/models/roles-and-permissions/platform-section.model';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { PermissionLevelEnum } from 'src/modules/roles-and-permissions/permissions/enums/enum';

@Injectable()
export class UserPermissionSeeder {
  constructor(
    @InjectModel(Permission)
    private readonly permissionModel: typeof Permission,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(PlatformSection)
    private readonly sectionModel: typeof PlatformSection,
  ) {}

  async seed() {
    try {
      const roles = await this.roleModel.findAll({
        where: {
          scope: ROLE_SCOPE_ENUM.COMPANY,
        },
      });

      const sections = await this.sectionModel.findAll();

      // Dynamically map roles and sections
      const roleMap = new Map<string, number>();
      roles.forEach((r) => roleMap.set(r.name.toLowerCase(), r.id));

      const sectionMap = new Map<string, number>();
      sections.forEach((s) => sectionMap.set(s.name.toLowerCase(), s.id));

      // Dynamically assign role variables
      const companySuperAdmin = roleMap.get('company super admin');
      const companyAdmin = roleMap.get('company admin');

      if (!companySuperAdmin || !companyAdmin) {
        throw new Error('Required roles not found.');
      }

      const permissions = [
        // Super Admin gets everything
        ...Array.from(sectionMap.entries()).map(([platformSectionName]) => ({
          roleId: companySuperAdmin,
          platformSectionName,
          level: PermissionLevelEnum.COMPANY,
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: true,
        })),

        // Admin only gets Time Attendance
        {
          roleId: companyAdmin,
          platformSectionName: 'Time Attendence',
          level: PermissionLevelEnum.COMPANY,
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: false,
        },
      ];

      for (const perm of permissions) {
        const sectionId = sectionMap.get(
          perm.platformSectionName.toLowerCase(),
        );
        if (!sectionId) {
          console.warn(`Section not found: ${perm.platformSectionName}`);
          continue;
        }

        await this.permissionModel.findOrCreate({
          where: {
            roleId: perm.roleId,
            platformSectionId: sectionId,
          },
          defaults: {
            roleId: perm.roleId,
            platformSectionId: sectionId,
            level: perm.level,
            canView: perm.canView,
            canCreate: perm.canCreate,
            canEdit: perm.canEdit,
            canDelete: perm.canDelete,
          },
        });
      }
    } catch (error) {
      console.error('Error seeding permissions:', error);
      throw error;
    }
  }
}
