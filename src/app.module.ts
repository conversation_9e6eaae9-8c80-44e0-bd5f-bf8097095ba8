import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import configuration from './config/configuration';
import { DatabaseModule } from './database/database.module';
import { UsersModule } from './modules/users/users.module';
import { AuthModule } from './modules/auth/auth.module';
import { EmployeesModule } from './modules/employees/employees.module';
import { DepartmentsModule } from './modules/departments/departments.module';
import { LeaveRequestsModule } from './modules/leave-requests/leave-requests.module';
import { TimeAttendanceModule } from './modules/time-attendance/time-attendance.module';
import { PayrollsModule } from './modules/payrolls/payrolls.module';
import { DocumentsModule } from './modules/documents/documents.module';
import { PerformanceReviewsModule } from './modules/performance-reviews/performance-reviews.module';
import { ProjectBonusesModule } from './modules/project-bonuses/project-bonuses.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { CompanySettingsModule } from './modules/company-settings/company-settings.module';
import { ProjectsModule } from './modules/projects/projects.module';
import { CrudHelperModule } from './common/crud-helper/crud-helper.module';
import { APP_INTERCEPTOR, APP_GUARD } from '@nestjs/core';
import { ResponseInterceptor } from './common/dto/interceptors/response.interceptor';
import { RolesModule } from './modules/roles-and-permissions/roles/roles.module';
import { PermissionsModule } from './modules/roles-and-permissions/permissions/permissions.module';
import { UserRoleModule } from './modules/roles-and-permissions/user-role/user-role.module';
import { PlatformSectionsModule } from './modules/roles-and-permissions/platform-sections/platform-sections.module';
import { CompanyModule } from './modules/company/company.module';
import { PermissionGuard } from './modules/auth/guards/permission.guard';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
      load: [configuration],
    }),
    RolesModule,
    PermissionsModule,
    UserRoleModule,
    PlatformSectionsModule,
    PermissionsModule,
    DatabaseModule,
    UsersModule,
    AuthModule,
    EmployeesModule,
    DepartmentsModule,
    LeaveRequestsModule,
    TimeAttendanceModule,
    PayrollsModule,
    DocumentsModule,
    PerformanceReviewsModule,
    ProjectBonusesModule,
    NotificationsModule,
    DashboardModule,
    CompanySettingsModule,
    ProjectsModule,
    CrudHelperModule,
    UserRoleModule,
    CompanyModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: PermissionGuard,
    },
  ],
})
export class AppModule {}
