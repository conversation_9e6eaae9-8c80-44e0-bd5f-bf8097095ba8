import { Column, Model, Table, DataType } from 'sequelize-typescript';

@Table({})
export class CompanySettings extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.ARRAY(DataType.STRING),
    allowNull: false,
  })
  workingDays: string[];

  @Column({
    type: DataType.TIME,
    allowNull: false,
  })
  workingHoursStart: string;

  @Column({
    type: DataType.TIME,
    allowNull: false,
  })
  workingHoursEnd: string;

  @Column({
    type: DataType.TIME,
    allowNull: false,
  })
  flexibleEntryStart: string;

  @Column({
    type: DataType.TIME,
    allowNull: false,
  })
  flexibleEntryEnd: string;

  @Column({
    type: DataType.DECIMAL,
    allowNull: false,
  })
  minWorkingHours: number;

  @Column({
    type: DataType.ARRAY(DataType.DATEONLY),
  })
  holidayDays: Date[];

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    allowNull: false,
  })
  updatedAt: Date;
}
