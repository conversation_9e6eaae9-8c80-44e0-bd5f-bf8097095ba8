import { Column, DataType, HasMany, Model, Table } from 'sequelize-typescript';
import { Permission } from './permission.model';

@Table({})
export class PlatformSection extends Model {
  @Column({ primaryKey: true, autoIncrement: true, type: DataType.INTEGER })
  id: number;

  @Column({ type: DataType.STRING, allowNull: false, unique: true })
  name: string;

  @HasMany(() => Permission)
  permissions: Permission[];
}
