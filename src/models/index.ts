import { Address } from './address-models/address-model';
import { Country } from './address-models/country-model';
import { CompanySettings } from './company-settings.model';
import { Department } from './department.model';
import { Document } from './document.model';
import { Event } from './event.model';
import { LeaveRequest } from './leave-request.model';
import { Message } from './message.model';
import { NotificationSetting } from './notification-setting.model';
import { Notification } from './notification.model';
import { OvertimeRule } from './overtime-rule.model';
import { Payroll } from './payroll.model';
import { PerformanceReview } from './performance-review.model';
import { ProjectBonus } from './project-bonus.model';
import { ProjectEmployee } from './project-employee.model';
import { Project } from './project.model';
import { Role } from './roles-and-permissions/role.model';
import { Permission } from './roles-and-permissions/permission.model';
import { UserRole } from './roles-and-permissions/user-role.model';
import { TimeAttendance } from './time-attendance.model';
import { User } from './users-models/user.model';
import { Company } from './company.model';
import { PlatformSection } from './roles-and-permissions/platform-section.model';
import { UserDetails } from './users-models/user-details.model';
import { ContactDetails } from './users-models/contact-details.model';
import { EmploymentDetails } from './users-models/employment-details.model';
import { BankInfo } from './users-models/bank-info.model';

export const modelsList = [
  User,
  UserDetails,
  ContactDetails,
  Role,
  UserRole,
  Permission,
  PlatformSection,
  Country,
  TimeAttendance,
  LeaveRequest,
  Document,
  Payroll,
  PerformanceReview,
  Project,
  ProjectEmployee,
  ProjectBonus,
  Department,
  Company,
  CompanySettings,
  EmploymentDetails,
  OvertimeRule,
  Event,
  NotificationSetting,
  Notification,
  Message,
  Address,
  BankInfo,
];
