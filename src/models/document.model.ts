import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './users-models/user.model';

@Table({})
export class Document extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @Column({
    type: DataType.STRING,
  })
  title: string;

  @Column({
    type: DataType.STRING,
  })
  description: string;

  @Column({
    type: DataType.STRING,
  })
  documentUrl: string;

  @Column({
    type: DataType.STRING,
  })
  documentType: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
  })
  uploadedBy: number;

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
  })
  uploadedAt: Date;

  @Column({
    type: DataType.TEXT,
  })
  content: string;

  @Column({
    type: DataType.STRING,
  })
  status: string;

  @Column({
    type: DataType.DATE,
  })
  dateCreated: Date;

  // Relationships
  @BelongsTo(() => User, { foreignKey: 'userId' })
  user: User;

  @BelongsTo(() => User, { foreignKey: 'uploadedBy' })
  uploader: User;
}
