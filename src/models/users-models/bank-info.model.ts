import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
} from 'sequelize-typescript';
import { User } from './user.model';

@Table({})
export class BankInfo extends Model {
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @Column({ type: DataType.STRING, allowNull: false })
  holderName: string;

  @Column({ type: DataType.STRING, allowNull: false })
  bankName: string;

  @Column({ type: DataType.STRING, allowNull: false })
  branchName: string;

  @Column({ type: DataType.STRING, allowNull: false })
  accountNumber: string;

  @Column({ type: DataType.STRING })
  iban: string;

  @Column({ type: DataType.STRING })
  swiftCode: string;

  @Column({ type: DataType.STRING })
  bankAddress: string;

  @Column({ type: DataType.STRING })
  currency: string;

  @Column({ type: DataType.BOOLEAN })
  isPrimary: boolean;
}
