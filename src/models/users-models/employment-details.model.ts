import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './user.model';
import { Company } from '../company.model';
import { Department } from '../department.model';

@Table({})
export class EmploymentDetails extends Model {
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @BelongsTo(() => User)
  user: User;

  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ForeignKey(() => Department)
  @Column({ type: DataType.INTEGER, allowNull: false })
  departmentId: number;

  @Column({ type: DataType.STRING })
  companyEmail: string;

  @Column({ type: DataType.STRING })
  position: string;

  @Column({ type: DataType.FLOAT })
  salary: number;

  @Column({ type: DataType.DATE })
  hireDate: Date;
}
