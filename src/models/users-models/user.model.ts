import {
  Column,
  Model,
  Table,
  <PERSON>Type,
  HasMany,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Country } from '../address-models/country-model';
import { Department } from '../department.model';
import { Address } from '../address-models/address-model';
import { TimeAttendance } from '../time-attendance.model';
import { LeaveRequest } from '../leave-request.model';
import { Payroll } from '../payroll.model';
import { PerformanceReview } from '../performance-review.model';
import {
  GENDER_TYPES_ENUM,
  MARITAL_STATUS_ENUM,
  USER_ACCOUNT_STATUS_ENUM,
} from 'src/utils/enums';
import { UserDetails } from './user-details.model';
import { ContactDetails } from './contact-details.model';
import { EmploymentDetails } from './employment-details.model';
import { BankInfo } from './bank-info.model';
import { UserRole } from '../roles-and-permissions/user-role.model';

@Table({
  defaultScope: {
    attributes: {
      exclude: ['password', 'refreshToken'],
    },
  },
  scopes: {
    withPassword: {
      attributes: {
        include: ['password'],
      },
    },
    withRefreshToken: {
      attributes: {
        include: ['refreshToken'],
      },
    },
    withSensitiveData: {
      attributes: {
        include: ['password', 'refreshToken'],
      },
    },
  },
})
export class User extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  username: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  email: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  password: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  firstName: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  lastName: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    unique: true,
  })
  refreshToken: string;

  @Column({
    type: DataType.ENUM(...Object.values(USER_ACCOUNT_STATUS_ENUM)),
    defaultValue: USER_ACCOUNT_STATUS_ENUM.ACTIVE,
  })
  status: USER_ACCOUNT_STATUS_ENUM;

  @Column({
    type: DataType.STRING,
  })
  profileImage: string;

  @Column({
    type: DataType.STRING,
  })
  redirectTo: string;

  @HasMany(() => UserDetails)
  userDetails: UserDetails[];

  @HasMany(() => UserRole)
  userRoles: UserRole[];

  @HasMany(() => ContactDetails)
  contactDetails: ContactDetails[];

  @HasMany(() => EmploymentDetails)
  employmentDetails: EmploymentDetails[];

  @HasMany(() => BankInfo)
  bankInfos: BankInfo[];

  // Belongs To Relationships

  @HasMany(() => Address)
  addresses: Address[];

  @HasMany(() => TimeAttendance)
  timeAttendances: TimeAttendance[];

  @HasMany(() => LeaveRequest)
  leaveRequests: LeaveRequest[];

  @HasMany(() => Payroll)
  payrolls: Payroll[];

  @HasMany(() => PerformanceReview)
  performanceReviews: PerformanceReview[];
}
