import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
} from 'sequelize-typescript';
import { User } from './user.model';
import { Country } from '../address-models/country-model';
import { GENDER_TYPES_ENUM, MARITAL_STATUS_ENUM } from 'src/utils/enums';

@Table({})
export class UserDetails extends Model {
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @ForeignKey(() => Country)
  @Column({ type: DataType.INTEGER, allowNull: false })
  countryId: number;

  @Column({ type: DataType.DATE, allowNull: false })
  dob: Date;

  @Column({
    type: DataType.ENUM(...Object.values(GENDER_TYPES_ENUM)),
    allowNull: false,
  })
  gender: GENDER_TYPES_ENUM;

  @Column({ type: DataType.STRING })
  passportNumber: string;

  @Column({ type: DataType.STRING })
  visaNumber: string;

  @Column({ type: DataType.FLOAT })
  salary: number;

  @Column({ type: DataType.DATE })
  hireDate: Date;

  @Column({ type: DataType.ENUM(...Object.values(MARITAL_STATUS_ENUM)) })
  maritalStatus: MARITAL_STATUS_ENUM;

  @Column({ type: DataType.STRING })
  religion: string;
}
