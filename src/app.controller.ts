import { Controller, Get, Res } from '@nestjs/common';
import { AppService } from './app.service';
import { PublicRoute } from './modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { Response } from 'express';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @PublicRoute()
  getHomePage(@Res() res: Response): void {
    return this.appService.getHomePage(res);
  }

  @Get('health')
  @PublicRoute()
  async getHealth() {
    return this.appService.getHealthCheck();
  }
}
